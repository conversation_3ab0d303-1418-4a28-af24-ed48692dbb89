#!/usr/bin/env python3
"""
Test the improved JSON repair system that preserves original data
"""

import requests
import json

def test_improved_json_repair():
    """Test the improved JSON repair that preserves original extracted data"""
    
    print("🧪 Testing Improved JSON Repair System")
    print("=" * 60)
    
    # Test case similar to your actual error - extracted data with syntax error
    malformed_json_with_data = """{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "experience": [
    {
      "title": "Java/J2EE Developer",
      "company": "Target",
      "location": "Minneapolis, MN",
      "duration": "Dec 2015 to Feb 2018",
      "responsibilities": [
        "Engineered microservices architecture using J2EE and Spring, ensuring modular and maintainable backend solutions.",
        "Developed an event-driven microservices architecture using Apache Kafka, enabling real-time data streaming and processing for enhanced scalability and reliability.",
        "Implemented RESTful APIs and integrated with various databases including Oracle and MySQL."
      ]
    },
    {
      "title": "Senior Software Engineer", 
      "company": "Microsoft",
      "location": "Seattle, WA",
      "duration": "Mar 2018 to Present",
      "responsibilities": [
        "Led development of cloud-native applications using Azure services.",
        "Mentored junior developers and conducted code reviews.",
        "Optimized application performance resulting in 40% improvement."
      ]
    }
  ],
  "skills": [
    "Java",
    "Spring Boot",
    "Apache Kafka",
    "Microservices",
    "Azure",
    "Docker",
    "Kubernetes",
    "Oracle",
    "MySQL",
    "REST APIs",
    "Agile",
    "Scrum",
    "Git",
    "Jenkins",
    "Confluence"
  ]
}"""  # Missing comma after skills array - this will cause the delimiter error

    print("📄 Testing with realistic extracted data that has JSON syntax error...")
    print(f"JSON length: {len(malformed_json_with_data)} characters")
    print(f"Contains actual extracted data: ✅")
    
    try:
        # First verify it's actually malformed
        try:
            json.loads(malformed_json_with_data)
            print("⚠️  JSON is actually valid - adding deliberate error...")
            # Add a deliberate syntax error
            malformed_json_with_data = malformed_json_with_data.replace('"Confluence"', '"Confluence",')  # Add trailing comma
            malformed_json_with_data = malformed_json_with_data.replace(']', ']\n  "education": []')  # Missing comma
        except json.JSONDecodeError as e:
            print(f"✅ JSON is malformed as expected: {e}")
        
        # Test the repair endpoint
        print("\n🔧 Sending to LLM for repair...")
        
        response = requests.post(
            "http://localhost:8000/debug/repair-json",
            json={
                "malformed_json": malformed_json_with_data,
                "context": "resume"
            },
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        print(f"📡 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Repair Status: {result['status']}")
            print(f"📄 Message: {result['message']}")
            
            if result['status'] == 'repaired':
                print(f"\n🎉 SUCCESS! JSON was repaired by LLM")
                
                # Get the repaired JSON
                repaired_json = result.get('repaired_json', '')
                parsed_data = result.get('parsed_data', {})
                
                print(f"\n📊 Repair Stats:")
                stats = result.get('repair_stats', {})
                print(f"   - Original length: {stats.get('original_length', 'N/A')}")
                print(f"   - Repaired length: {stats.get('repaired_length', 'N/A')}")
                
                # Verify the repaired JSON contains the original data
                print(f"\n🔍 Data Preservation Check:")
                
                if 'name' in parsed_data:
                    print(f"   ✅ Name preserved: {parsed_data['name']}")
                else:
                    print(f"   ❌ Name missing")
                
                if 'email' in parsed_data:
                    print(f"   ✅ Email preserved: {parsed_data['email']}")
                else:
                    print(f"   ❌ Email missing")
                
                if 'experience' in parsed_data and len(parsed_data['experience']) > 0:
                    exp_count = len(parsed_data['experience'])
                    print(f"   ✅ Experience preserved: {exp_count} entries")
                    
                    # Check first experience entry
                    first_exp = parsed_data['experience'][0]
                    if 'title' in first_exp:
                        print(f"      - First job title: {first_exp['title']}")
                    if 'company' in first_exp:
                        print(f"      - First company: {first_exp['company']}")
                else:
                    print(f"   ❌ Experience missing or empty")
                
                if 'skills' in parsed_data and len(parsed_data['skills']) > 0:
                    skills_count = len(parsed_data['skills'])
                    print(f"   ✅ Skills preserved: {skills_count} skills")
                    print(f"      - Sample skills: {parsed_data['skills'][:5]}")
                else:
                    print(f"   ❌ Skills missing or empty")
                
                # Check if it's returning schema template instead of data
                if isinstance(parsed_data.get('name'), str) and 'string' in parsed_data['name']:
                    print(f"\n❌ PROBLEM DETECTED: LLM returned schema template instead of actual data!")
                    print(f"   Expected: 'John Doe'")
                    print(f"   Got: '{parsed_data['name']}'")
                else:
                    print(f"\n✅ SUCCESS: LLM preserved actual extracted data!")
                    
            elif result['status'] == 'already_valid':
                print(f"\n💡 JSON was already valid")
            else:
                print(f"\n❌ Repair failed: {result.get('message', 'Unknown error')}")
                if 'repair_error' in result:
                    print(f"🔍 Repair error: {result['repair_error']}")
                    
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out - LLM repair took too long")
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to the API server")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def test_simple_syntax_errors():
    """Test simple syntax errors to ensure they're fixed correctly"""
    
    print(f"\n🔧 Testing Simple Syntax Error Fixes")
    print("=" * 60)
    
    test_cases = [
        {
            "name": "Trailing comma in array",
            "json": '{"name": "John", "skills": ["Python", "Java",]}',
            "expected_fix": "Remove trailing comma"
        },
        {
            "name": "Missing comma between properties",
            "json": '{"name": "John" "email": "<EMAIL>"}',
            "expected_fix": "Add missing comma"
        },
        {
            "name": "Missing closing brace",
            "json": '{"name": "John", "skills": ["Python"]}',
            "expected_fix": "Should be valid already"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📄 Test {i}: {test_case['name']}")
        print(f"Expected: {test_case['expected_fix']}")
        
        try:
            response = requests.post(
                "http://localhost:8000/debug/repair-json",
                json={
                    "malformed_json": test_case['json'],
                    "context": "resume"
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"   Status: {result['status']}")
                
                if result['status'] == 'repaired':
                    parsed = result.get('parsed_data', {})
                    if 'name' in parsed and parsed['name'] == 'John':
                        print(f"   ✅ Data preserved correctly")
                    else:
                        print(f"   ❌ Data not preserved: {parsed.get('name', 'missing')}")
                elif result['status'] == 'already_valid':
                    print(f"   ✅ JSON was already valid")
            else:
                print(f"   ❌ Error: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")

if __name__ == "__main__":
    print("🚀 Improved JSON Repair System Test")
    print("Testing the updated system that preserves original extracted data...")
    
    # Test 1: Realistic data with syntax error
    test_improved_json_repair()
    
    # Test 2: Simple syntax errors
    test_simple_syntax_errors()
    
    print("\n" + "=" * 60)
    print("🎉 Testing completed!")
    print("\n📋 Key Improvements Tested:")
    print("✅ Removed basic regex fixes - goes straight to LLM")
    print("✅ Updated prompt to preserve original extracted data")
    print("✅ Prevents LLM from returning schema templates")
    print("✅ Maintains all original field names and values")
    print("✅ Only fixes JSON syntax errors")
    
    print("\n🎯 Expected Behavior:")
    print("1. LLM receives malformed JSON with actual extracted data")
    print("2. LLM fixes ONLY the syntax errors (commas, brackets, quotes)")
    print("3. LLM returns the SAME data with correct JSON syntax")
    print("4. No schema templates or placeholder values returned")
