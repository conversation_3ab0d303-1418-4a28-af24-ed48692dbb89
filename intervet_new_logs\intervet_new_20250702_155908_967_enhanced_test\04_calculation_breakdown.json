{"timestamp": "20250702_155908_967", "calculation_method": "CGPA", "total_credits_used": 13.0, "processing_time_seconds": 0.0020003318786621094, "final_score": 7.884615384615385, "fit_category": "Strong Match", "summary": "The candidate is a strong match for this position with a CGPA-style score of 7.9/10. Key strengths: Skills, Education, Location. Areas for improvement: Certifications.", "field_scores": {"skills": {"raw_score": 9.0, "weight": 5.0, "weighted_score": 45.0, "rationale": "Excellent skills match - has 8/8 required skills and 2/4 preferred skills", "details": {"resume_skills_count": 12, "required_skills_count": 8, "preferred_skills_count": 4, "matched_required": ["Python", "JavaScript", "React", "Node.js", "SQL", "REST APIs", "Git", "AWS"], "missing_required": [], "matched_preferred": ["<PERSON>er", "MongoDB"], "required_match_ratio": 1.0, "preferred_match_ratio": 0.5, "_calculation_steps": ["Step 1: Required skills score = 8/8 × 8 = 8.00", "Step 2: Preferred skills bonus = 2/4 × 2 = 1.00", "Step 3: Total score = min(10, 8.00 + 1.00) = 9.00"], "_scoring_formula": "Score = min(10, (matched_required/total_required × 8) + (matched_preferred/total_preferred × 2))", "_explanation": "Required skills contribute up to 8 points (80%), preferred skills add up to 2 bonus points (20%)"}}, "experience": {"raw_score": 6.0, "weight": 3.0, "weighted_score": 18.0, "rationale": "Good experience level - 8 years is appropriate for this role", "details": {"candidate_yoe": 8, "required_yoe": 3, "experience_ratio": 2.6666666666666665, "experience_entries_count": 3, "_experience_breakdown": [{"company": "Tech Corp", "position": "Senior Software Engineer", "duration": "2020 - Present", "years_calculated": 4, "calculation_method": "Year range: 2020 to 2024"}, {"company": "StartupXYZ", "position": "Full Stack Developer", "duration": "2018 - 2020", "years_calculated": 2, "calculation_method": "Year range: 2018 to 2020"}, {"company": "WebDev Inc", "position": "Junior Developer", "duration": "2016 - 2018", "years_calculated": 2, "calculation_method": "Year range: 2016 to 2018"}], "_calculation_steps": ["Step 1: Required experience extracted: 3 years from '3+ years of software development experience'", "Step 2: Analyzing 3 experience entries", "  Entry 1: Tech Corp - Senior Software Engineer (2020 - Present) = 4 years", "  Entry 2: StartupXYZ - Full Stack Developer (2018 - 2020) = 2 years", "  Entry 3: WebDev Inc - Junior Developer (2016 - 2018) = 2 years", "Step 2 Result: Total candidate experience = 8 years", "Step 3: Calculating experience score", "  Experience ratio: 8/3 = 2.67", "  ~ Significantly over-experienced (>2.5): Score = 6.0"], "_scoring_formula": "Score based on experience ratio: 10 for 0.8-1.5x, 7-10 for 0.6-0.8x, 8 for 1.5-2.5x, 6 for >2.5x, 2-6 for <0.6x", "_explanation": "Experience scoring rewards candidates with appropriate experience levels, penalizing both under and over-qualification"}}, "education": {"raw_score": 10.0, "weight": 2.0, "weighted_score": 20.0, "rationale": "Perfect education match - meets all degree requirements", "details": {"education_entries_count": 1, "education_requirements_count": 1, "education_match": true, "partial_match": false, "matched_degree": "Bachelor of Science in Computer Science", "matched_requirement": "Bachelor's degree in Computer Science or related field", "match_type": "exact_match", "candidate_degrees": ["Bachelor of Science in Computer Science"], "required_degrees": ["Bachelor's degree in Computer Science or related field"], "_calculation_steps": ["Step 1: Checking 1 education requirement(s)", "  - Analyzing requirement: 'Bachelor's degree in Computer Science or related field'", "    Required degree type: bachelor", "    Required field: computer science", "    Candidate degree: 'Bachelor of Science in Computer Science' (Type: bachelor, Field: computer science)", "    ✓ EXACT MATCH FOUND: Degree type and field match", "Step 2: Applying binary scoring system", "  ✓ Education requirement met: Score = 10.0"], "_scoring_formula": "Binary scoring: 10 for exact match, 6 for partial match (benefit of doubt), 0 for no match, 5 for no requirements", "_explanation": "Education scoring uses binary logic: candidates either meet the requirements (10 points) or don't (0 points), with benefit of doubt for related degrees (6 points)"}}, "certifications": {"raw_score": 4.0, "weight": 1.5, "weighted_score": 6.0, "rationale": "Good certifications - 2 relevant certifications add value", "details": {"total_certifications": 2, "relevant_certifications": ["AWS Certified Developer", "React Developer Certification"], "relevant_count": 2, "all_certifications": ["AWS Certified Developer", "React Developer Certification"], "_irrelevant_certifications": [], "_certification_analysis": [{"certification": "AWS Certified Developer", "relevant": true, "matched_skills": ["AWS"], "points_awarded": 2}, {"certification": "React Developer Certification", "relevant": true, "matched_skills": ["React"], "points_awarded": 2}], "_calculation_steps": ["Step 1: Found 2 certifications in resume", "Step 2: Checking relevance against 12 job skills (8 required + 4 preferred)", "  Cert 1: 'AWS Certified Developer' - RELEVANT (matches: AWS) = +2 points", "  Cert 2: 'React Developer Certification' - RELEVANT (matches: React) = +2 points", "Step 3: Score calculation", "  Base score: 2 relevant certs × 2 points = 4", "  Final score: min(10, 4) = 4"], "_scoring_formula": "Score = min(10, relevant_certifications_count × 2)", "_explanation": "Each relevant certification (matching job skills) contributes 2 points, with a maximum score of 10"}}, "location": {"raw_score": 10.0, "weight": 1.0, "weighted_score": 10.0, "rationale": "Excellent location match - candidate is in the job location", "details": {"jd_location": "san francisco, ca", "resume_location": "san francisco, ca", "experience_locations": ["san francisco, ca", "san francisco, ca", "san jose, ca"], "has_location_info": true, "_calculation_steps": ["Step 1: Location extraction", "  Job location: 'san francisco, ca' (from JD)", "  Resume location: 'san francisco, ca' (from resume)", "  Experience locations: ['san francisco, ca', 'san francisco, ca', 'san jose, ca'] (from work history)", "Step 2: Location matching analysis", "  ✓ Current location match: Score = 10.0"], "_scoring_formula": "10 for current location match, 7 for previous work location match, 3 for no match, 5 for insufficient data", "_explanation": "Location scoring prioritizes current location match, gives credit for previous work experience in the job location"}}, "reliability": {"raw_score": 7.0, "weight": 0.5, "weighted_score": 3.5, "rationale": "Good job stability - average 2.7 years per company is reasonable", "details": {"candidate_yoe": 8, "num_companies": 3, "avg_tenure": 2.6666666666666665, "has_experience_data": true, "_tenure_breakdown": [{"company": "Tech Corp", "duration": "2020 - Present", "years_calculated": 4, "calculation_method": "Year range: 2020 to 2024"}, {"company": "StartupXYZ", "duration": "2018 - 2020", "years_calculated": 2, "calculation_method": "Year range: 2018 to 2020"}, {"company": "WebDev Inc", "duration": "2016 - 2018", "years_calculated": 2, "calculation_method": "Year range: 2016 to 2018"}], "_calculation_steps": ["Step 1: Analyzing 3 experience entries for tenure calculation", "  Entry 1: Tech Corp (2020 - Present) = 4 years", "  Entry 2: StartupXYZ (2018 - 2020) = 2 years", "  Entry 3: WebDev Inc (2016 - 2018) = 2 years", "Step 1 Result: Total experience = 8 years", "Step 2: Calculating job stability/reliability", "  Total companies: 3", "  Total years: 8", "  Average tenure: 8/3 = 2.67 years per company", "  ~ Good stability (2-3 years): Score = 7.0"], "_scoring_formula": "Score based on average tenure: 10 for ≥4 years, 8.5 for 3-4 years, 7 for 2-3 years, 5 for 1.5-2 years, 3 for 1-1.5 years, 1 for <1 year", "_explanation": "Reliability scoring measures job stability through average tenure per company, rewarding consistent employment history"}}}}