
INTERVET_NEW CALCULATION SUMMARY
================================
Timestamp: 20250702_153850_946
Processing Time: 0.002 seconds

FINAL RESULT
============
Total Score: 7.88/10
Fit Category: Strong Match
Summary: The candidate is a strong match for this position with a CGPA-style score of 7.9/10. Key strengths: Skills, Education, Location. Areas for improvement: Certifications.

WEIGHTAGE CONFIGURATION
=======================
Skills: 5.0
Experience: 3.0
Education: 2.0
Certifications: 1.5
Location: 1.0
Reliability: 0.5
Total Credits: 13.0

DETAILED FIELD SCORES
=====================
Skills:
  Raw Score: 9.00/10
  Weight: 5.0
  Weighted Score: 45.00
  Rationale: Matched 8/8 required skills and 2/4 preferred skills. Matched: Python, JavaScript, React, Node.js, SQL, REST APIs, Git, AWS

Experience:
  Raw Score: 6.00/10
  Weight: 3.0
  Weighted Score: 18.00
  Rationale: Moderate match: 8 years vs required 3 years (significantly over-experienced, ratio: 2.67)

Education:
  Raw Score: 10.00/10
  Weight: 2.0
  Weighted Score: 20.00
  Rationale: Education requirements fully met: 'Bachelor of Science in Computer Science' matches requirement 'Bachelor's degree in Computer Science or related field' (Match type: exact_match)

Certifications:
  Raw Score: 4.00/10
  Weight: 1.5
  Weighted Score: 6.00
  Rationale: Found 2 relevant certifications: AWS Certified Developer, React Developer Certification

Location:
  Raw Score: 10.00/10
  Weight: 1.0
  Weighted Score: 10.00
  Rationale: Current location (san francisco, ca) matches job location (san francisco, ca)

Reliability:
  Raw Score: 7.00/10
  Weight: 0.5
  Weighted Score: 3.50
  Rationale: Good stability: average 2.7 years per company

CALCULATION FORMULA
===================
Final Score = (Sum of Weighted Scores) / (Sum of Weights)
Final Score = (45.00 + 18.00 + 20.00 + 6.00 + 10.00 + 3.50) / 13.0
Final Score = 7.88/10
