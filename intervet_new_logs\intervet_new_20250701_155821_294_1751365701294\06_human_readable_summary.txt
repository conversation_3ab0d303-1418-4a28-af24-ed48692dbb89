
INTERVET_NEW CALCULATION SUMMARY
================================
Timestamp: 20250701_155821_294
Processing Time: 0.002 seconds

FINAL RESULT
============
Total Score: 3.35/10
Fit Category: Weak Match
Summary: The candidate is a weak match for this position with a CGPA-style score of 3.4/10. Key strengths: Education. Areas for improvement: Skills, Certifications.

WEIGHTAGE CONFIGURATION
=======================
Skills: 4.0
Experience: 3.0
Education: 2.0
Certifications: 0.5
Location: 0.5
Reliability: 0.0
Total Credits: 10.0

DETAILED FIELD SCORES
=====================
Skills:
  Raw Score: 0.00/10
  Weight: 4.0
  Weighted Score: 0.00
  Rationale: Matched 0/5 required skills and 0/1 preferred skills. Missing: HTML, CSS, Javascript, Media queries, SDLC

Experience:
  Raw Score: 5.00/10
  Weight: 3.0
  Weighted Score: 15.00
  Rationale: Could not determine candidate's experience to compare with required 1 years

Education:
  Raw Score: 8.00/10
  Weight: 2.0
  Weighted Score: 16.00
  Rationale: Education requirements met: 'Bachelor of Technology (Artificial Intelligence)' matches requirement 'Bachelor’s degree in IT, Computer Science, Software Engineering, or a related field.'

Certifications:
  Raw Score: 0.00/10
  Weight: 0.5
  Weighted Score: 0.00
  Rationale: Has 1 certifications but none are directly relevant to job requirements

Location:
  Raw Score: 5.00/10
  Weight: 0.5
  Weighted Score: 2.50
  Rationale: Location information not available for comparison

Reliability:
  Raw Score: 5.00/10
  Weight: 0.0
  Weighted Score: 0.00
  Rationale: Could not calculate job stability due to missing experience data

CALCULATION FORMULA
===================
Final Score = (Sum of Weighted Scores) / (Sum of Weights)
Final Score = (0.00 + 15.00 + 16.00 + 0.00 + 2.50 + 0.00) / 10.0
Final Score = 3.35/10
