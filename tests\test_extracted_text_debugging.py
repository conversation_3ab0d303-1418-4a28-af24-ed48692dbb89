#!/usr/bin/env python3
"""
Test script for the extracted text debugging functionality.

This script tests the new debugging feature that saves extracted text
from resumes and job descriptions to dedicated folders.
"""

import os
import sys
import tempfile
import time
from datetime import datetime

# Add the parent directory to the path so we can import from main.py
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from main import (
    save_extracted_text,
    create_extracted_text_filename,
    extract_text_from_file
)

def test_create_extracted_text_filename():
    """Test the filename creation function."""
    print("🧪 Testing create_extracted_text_filename function...")
    
    # Test resume context
    resume_filename = create_extracted_text_filename("mehak_jain.pdf", "resume")
    expected_resume = os.path.join("resume_extracted_text", "mehak_jain.txt")
    assert resume_filename == expected_resume, f"Expected {expected_resume}, got {resume_filename}"
    print(f"✅ Resume filename: {resume_filename}")
    
    # Test JD context
    jd_filename = create_extracted_text_filename("software_engineer_job.docx", "jd")
    expected_jd = os.path.join("jd_extracted_text", "software_engineer_job.txt")
    assert jd_filename == expected_jd, f"Expected {expected_jd}, got {jd_filename}"
    print(f"✅ JD filename: {jd_filename}")
    
    # Test with special characters
    special_filename = create_extracted_text_filename("john doe's resume (updated).pdf", "resume")
    expected_special = os.path.join("resume_extracted_text", "john_doe_s_resume__updated_.txt")
    assert special_filename == expected_special, f"Expected {expected_special}, got {special_filename}"
    print(f"✅ Special characters filename: {special_filename}")
    
    print("✅ All filename creation tests passed!\n")

def test_save_extracted_text():
    """Test the save extracted text function."""
    print("🧪 Testing save_extracted_text function...")
    
    # Test data
    sample_text = """# Sample Resume Text
John Doe
Software Engineer
Email: <EMAIL>
Phone: (*************

EXPERIENCE:
- Software Developer at TechCorp (2020-2023)
- Junior Developer at StartupInc (2018-2020)

SKILLS:
- Python, JavaScript, React
- AWS, Docker, Kubernetes
- Machine Learning, Data Analysis
"""
    
    # Test saving resume text
    resume_path = save_extracted_text(
        sample_text, 
        "john_doe_resume.pdf", 
        "resume", 
        "pdf_text"
    )
    
    assert os.path.exists(resume_path), f"Resume file was not created: {resume_path}"
    print(f"✅ Resume text saved to: {resume_path}")
    
    # Verify content
    with open(resume_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    assert sample_text in content, "Original text not found in saved file"
    assert "# Extracted Text Debug File" in content, "Metadata header not found"
    assert "john_doe_resume.pdf" in content, "Source filename not found in metadata"
    assert "pdf_text" in content, "Extraction method not found in metadata"
    print("✅ File content verification passed")
    
    # Test saving JD text
    jd_sample = """Software Engineer Job Description
Company: TechCorp
Location: San Francisco, CA

Requirements:
- 3+ years of Python experience
- Experience with cloud platforms (AWS/Azure)
- Strong problem-solving skills
"""
    
    jd_path = save_extracted_text(
        jd_sample,
        "software_engineer_jd.pdf",
        "jd",
        "image_based"
    )
    
    assert os.path.exists(jd_path), f"JD file was not created: {jd_path}"
    print(f"✅ JD text saved to: {jd_path}")
    
    print("✅ All save extracted text tests passed!\n")

def test_folder_creation():
    """Test that folders are created automatically."""
    print("🧪 Testing automatic folder creation...")
    
    # Remove folders if they exist
    test_folders = ["resume_extracted_text", "jd_extracted_text"]
    for folder in test_folders:
        if os.path.exists(folder):
            # Remove all files in the folder first
            for file in os.listdir(folder):
                os.remove(os.path.join(folder, file))
            os.rmdir(folder)
    
    # Test that folders are created when saving
    test_text = "Test content for folder creation"
    
    resume_path = save_extracted_text(test_text, "test_resume.pdf", "resume", "test")
    jd_path = save_extracted_text(test_text, "test_jd.pdf", "jd", "test")
    
    assert os.path.exists("resume_extracted_text"), "Resume folder was not created"
    assert os.path.exists("jd_extracted_text"), "JD folder was not created"
    assert os.path.exists(resume_path), "Resume file was not created"
    assert os.path.exists(jd_path), "JD file was not created"
    
    print("✅ Folders created automatically")
    print("✅ All folder creation tests passed!\n")

def test_integration_with_extract_text_from_file():
    """Test integration with the main extraction function."""
    print("🧪 Testing integration with extract_text_from_file...")
    
    # Create a simple test PDF content (we'll use the existing test file)
    test_file_path = "test_resume_mehak_jain.txt"
    
    if os.path.exists(test_file_path):
        # Read the test file content
        with open(test_file_path, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        # Create a temporary file to test with
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as temp_file:
            temp_file.write(original_content)
            temp_file_path = temp_file.name
        
        try:
            # Note: extract_text_from_file expects PDF or DOCX, but for testing we'll simulate
            # We can't easily test the full integration without actual PDF/DOCX files
            print("⚠️  Full integration test requires actual PDF/DOCX files")
            print("✅ Integration test structure verified")
            
        finally:
            # Clean up
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
    else:
        print("⚠️  Test file not found, skipping integration test")
    
    print("✅ Integration test completed!\n")

def cleanup_test_files():
    """Clean up test files created during testing."""
    print("🧹 Cleaning up test files...")
    
    test_folders = ["resume_extracted_text", "jd_extracted_text"]
    files_removed = 0
    
    for folder in test_folders:
        if os.path.exists(folder):
            for file in os.listdir(folder):
                file_path = os.path.join(folder, file)
                if file.startswith(("john_doe", "test_", "software_engineer")):
                    os.remove(file_path)
                    files_removed += 1
                    print(f"  Removed: {file_path}")
    
    print(f"✅ Cleanup completed - removed {files_removed} test files\n")

def main():
    """Run all tests."""
    print("🚀 Starting Extracted Text Debugging Tests")
    print("=" * 50)
    
    try:
        test_create_extracted_text_filename()
        test_save_extracted_text()
        test_folder_creation()
        test_integration_with_extract_text_from_file()
        
        print("🎉 All tests passed successfully!")
        print("\n📁 Check the following folders for extracted text files:")
        print("   - resume_extracted_text/")
        print("   - jd_extracted_text/")
        
        print("\n🔧 New API endpoints available:")
        print("   - GET /extracted_text/list - List all extracted text files")
        print("   - GET /extracted_text/view/{context}/{filename} - View specific file")
        print("   - DELETE /extracted_text/cleanup - Clean up old files")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        cleanup_test_files()

if __name__ == "__main__":
    main()
