#!/usr/bin/env python3
"""
Test a single resume to debug the character replacement.
"""

import requests
import json
import time
from pathlib import Path

BASE_URL = "http://localhost:8000"

def test_single_resume():
    """Test MEESALA SREE SAI NATH resume specifically."""
    
    print("🧪 Testing Single Resume: MEESALA SREE SAI NATH")
    print("=" * 60)
    
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print("❌ Server not responding correctly")
            return
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        return
    
    resume_path = Path("resumes for testing/Resume-MEESALA SREE SAI NATH.pdf")
    
    if not resume_path.exists():
        print(f"❌ Resume not found: {resume_path}")
        return
    
    print(f"📄 Testing: {resume_path.name}")
    print(f"📊 File size: {resume_path.stat().st_size:,} bytes")
    
    try:
        # Test the hybrid endpoint
        start_time = time.time()
        
        with open(resume_path, 'rb') as f:
            files = {'file': (resume_path.name, f, 'application/pdf')}
            response = requests.post(f"{BASE_URL}/hybrid_resume", files=files, timeout=120)
        
        processing_time = time.time() - start_time
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"⏱️ Processing Time: {processing_time:.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            
            # Check if there's an error in the response
            if result.get('error'):
                print(f"⚠️ Response contains error: {result['error']}")
                return
            
            # Check the key data
            name = result.get('name', 'Unknown')
            education_count = len(result.get('education', []))
            skills_count = len(result.get('skills', []))
            projects_count = len(result.get('projects', []))
            experience_count = len(result.get('experience', []))
            
            print(f"📊 Results:")
            print(f"   👤 Name: '{name}'")
            print(f"   🎓 Education: {education_count} entries")
            print(f"   💼 Experience: {experience_count} entries")
            print(f"   🛠️ Skills: {skills_count} items")
            print(f"   🚀 Projects: {projects_count} items")
            
            # Check if we got meaningful data (not just "Unknown")
            if name == 'Unknown' and education_count == 0 and skills_count == 0:
                print(f"❌ FAILED: Still getting fallback response")
                print("📝 Check the latest prompt logs for details")
            else:
                print(f"✅ SUCCESS: Character replacement fixed the issue!")
                print(f"🎉 Name extracted: '{name}'")
                print(f"📊 Total data points: {education_count + skills_count + projects_count + experience_count}")
            
        else:
            print(f"❌ FAILED: HTTP {response.status_code}: {response.text}")
            
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")

if __name__ == "__main__":
    test_single_resume()
