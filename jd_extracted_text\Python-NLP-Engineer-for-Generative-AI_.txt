# Extracted Text Debug File
# Source File: Python-NLP-Engineer-for-Generative-AI_.pdf
# Context: jd
# Extraction Method: pdf_text
# Timestamp: 2025-07-02 15:45:53
# Text Length: 3677 characters
# ================================================

 
December 2023   ELTEMATE  
 
 
 
Job Description | Python NLP Engineer for Generative AI  
We are seeking a talented and experienced Python NLP Engineer with a Generative AI focus to join 
our growing team.  
 
1. ABOUT ELTEMATE  
We are ELTEMATE – A Hogan Lovells Legal Tech  Brand. Our goal is to make clients’ lives easier 
by delivering practical solutions to everyday problems. We combine a deep understanding of 
our clients' legal needs with the speed and innovation of a technology start-up. Our portfolio 
covers a large spectrum of legal tech solutions  including artificial intelligence, eDiscovery , 
information analysis , regulatory updates , databases , deal rooms , workflow  management, c ase 
management , document automation , risk assessment , reporting , and a pps. 
2. ROLE DESCRIPTION  
We are seeking a skilled Python NLP Engineer with a focus on Generative AI to join our 
dynamic team. The successful candidate will primarily work on developing our natural 
language processing capabilities a nd contribute to the evolution of our suite of legal 
generative -AI applications. This person will collaborate with product managers, backend and 
front -end developers and data scientists to deliver cutting -edge AI solutions.  
3. DUTIES AND RESPONSIBILITIES  
• Design, develop, and maintain sophisticated NLP and Generative AI applications in Python.  
• Design and implement back -end APIs to deliver Python services to front -end applications 
using FastAPI and Docker . 
• Write clean, efficient, and modular code adhering to  best practices and coding standards.  
• Optimize application performance and ensure scalability.  
• Collaborate in the database design and management of those systems (PostgreSQL or 
MongoDB).  
• Conduct thorough testing and debugging to identify and resolve issues . 
• Stay up -to-date with emerging trends and technologies in the field of Generative AI . 
4. REQUIRED KNOWLEDGE , SKILLS AND EXPERIENCE  
• Bachelor's degree in Computer Science, Engineering, Computational Linguistics, Data Science 
or a related quantitative field (or  equivalent experience).  
• Extensive experience working with Python, particularly in relation to NLP and AI.  
• Experience with modern LLM -based libraries such as LangChain, PyTorch, GPT, Transformers  
• Familiarity with software engineering best -practices such as  version control, testing, code 
reviews.  

- 2 - 
 
 
December 2023   ELTEMATE  • Strong problem -solving skills and ability to work in a fast -paced environment.  
• Excellent communication and collaboration skills.  
 
5. OTHER PREFERRED SKILLS  
• Demonstrable experience in semantic search and document retrie val tasks . 
• Knowledge of discriminative AI (classifiers) and associated accuracy metrics (F1, Precision, 
Recall) . 
• Some knowledge of JavaScript is a big advantage . 
 
6. OTHER DETAILS  
• Full-time employee.  
• Reports to Head of AI Development . 
• Located preferably in Amsterdam (Netherlands) but willing to be flexible (EU) and can 
accommodate agile work environments . 
• Compensation  in the range of € 70,000 – €90,000 depending on qualifications  and experience . 
 
7. DISCLAIMER  
ELTEMATE  is an equal employment opportunity employer and does not discriminate against 
any employee or applicant for employment ssss on the basis of race, color, religion, gender, 
national origin, age, sex, disability, veteran status, marital status, sexual orienta tion, gender 
identity  or any other characteristic protected by law.  
 
If you are interested, please get in touch with <NAME_EMAIL> . We look forward to meeting 
you. 
 
 
 
 
  
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
