#!/usr/bin/env python3
"""
Test script for /intervet_new endpoint using a real resume file
This script parses a real resume and tests with sample JD data
"""

import requests
import json
import time
from pathlib import Path

# Configuration
API_BASE_URL = "http://localhost:8002"
RESUME_ENDPOINT = f"{API_BASE_URL}/resume"
INTERVET_NEW_ENDPOINT = f"{API_BASE_URL}/intervet_new"

# Test files
RESUME_FILE = "resumes for testing/Resume-Raman Luhach.pdf"

def get_sample_jd_data():
    """Get sample job description data for testing"""
    return {
        "job_title": "Senior Software Engineer",
        "company_name": "TechCorp Solutions",
        "location": "Bangalore, India",
        "required_skills": [
            "Python", "Java", "JavaScript", "React", "Node.js", "SQL", 
            "REST APIs", "Git", "AWS", "Docker"
        ],
        "preferred_skills": [
            "Kubernetes", "Machine Learning", "TypeScript", "GraphQL"
        ],
        "required_experience": "5+ years of experience in software development",
        "education_requirements": [
            "Bachelor's degree in Computer Science or related field"
        ]
    }

def parse_resume_file(file_path):
    """Parse resume file using the /resume endpoint"""
    print(f"📄 Parsing resume file: {file_path}")
    
    try:
        with open(file_path, 'rb') as f:
            files = {'file': (Path(file_path).name, f, 'application/pdf')}
            response = requests.post(RESUME_ENDPOINT, files=files, timeout=180)
        
        if response.status_code == 200:
            resume_data = response.json()
            print(f"✅ Resume parsed successfully")
            print(f"   Name: {resume_data.get('name', 'Unknown')}")
            print(f"   Email: {resume_data.get('email', 'Not found')}")
            print(f"   Phone: {resume_data.get('phone', 'Not found')}")
            print(f"   Location: {resume_data.get('location', 'Not found')}")
            print(f"   Skills: {len(resume_data.get('skills', []))} skills found")
            print(f"   Experience: {len(resume_data.get('experience', []))} entries")
            print(f"   Education: {len(resume_data.get('education', []))} entries")
            print(f"   Projects: {len(resume_data.get('projects', []))} projects")
            print(f"   Certifications: {len(resume_data.get('certifications', []))} certifications")
            
            # Show some skills
            skills = resume_data.get('skills', [])
            if isinstance(skills, list) and skills:
                print(f"   Sample skills: {', '.join(skills[:5])}{'...' if len(skills) > 5 else ''}")
            elif isinstance(skills, dict) and skills:
                skill_names = list(skills.keys())[:5]
                print(f"   Sample skills: {', '.join(skill_names)}{'...' if len(skills) > 5 else ''}")
            
            return resume_data
        else:
            print(f"❌ Resume parsing failed: {response.status_code}")
            print(f"Error: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error parsing resume: {e}")
        return None

def test_intervet_new_with_real_resume(resume_data, jd_data, weightage_config=None, test_name=""):
    """Test the /intervet_new endpoint with real resume data"""
    print(f"\n🧪 Testing /intervet_new endpoint {test_name}...")
    
    request_data = {
        "resume_json": resume_data,
        "jd_json": jd_data
    }
    
    if weightage_config:
        request_data["weightage"] = weightage_config
        print(f"   Using custom weightage: {weightage_config}")
    else:
        print(f"   Using default weightage")
    
    try:
        start_time = time.time()
        response = requests.post(INTERVET_NEW_ENDPOINT, json=request_data, timeout=120)
        processing_time = time.time() - start_time
        
        print(f"⏱️  Processing time: {processing_time:.2f} seconds")
        print(f"📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ CGPA-style evaluation completed successfully!")
            print(f"\n📈 FINAL RESULTS:")
            print(f"   🎯 Total Score: {result['total_score']:.2f}/10")
            print(f"   📊 Fit Category: {result['fit_category']}")
            print(f"   ⚖️  Total Credits Used: {result['total_credits_used']}")
            print(f"   ⏱️  Processing Time: {result['processing_time']:.3f}s")
            
            print(f"\n💭 Summary:")
            print(f"   {result['summary']}")
            
            print(f"\n📋 DETAILED FIELD SCORES:")
            fields = [
                ('Skills', result['skills_score']),
                ('Experience', result['experience_score']),
                ('Education', result['education_score']),
                ('Certifications', result['certifications_score']),
                ('Location', result['location_score']),
                ('Reliability', result['reliability_score'])
            ]
            
            for field_name, field_data in fields:
                print(f"\n   {field_name}:")
                print(f"     Raw Score: {field_data['raw_score']:.2f}/10")
                print(f"     Weight: {field_data['weight']}")
                print(f"     Weighted Score: {field_data['weighted_score']:.2f}")
                print(f"     Rationale: {field_data['rationale']}")
                
                # Show some details for skills
                if field_name == 'Skills' and 'details' in field_data:
                    details = field_data['details']
                    if 'matched_required' in details and details['matched_required']:
                        print(f"     Matched Required: {', '.join(details['matched_required'][:3])}{'...' if len(details['matched_required']) > 3 else ''}")
                    if 'missing_required' in details and details['missing_required']:
                        print(f"     Missing Required: {', '.join(details['missing_required'][:3])}{'...' if len(details['missing_required']) > 3 else ''}")
            
            return True
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing /intervet_new with Real Resume File")
    print("=" * 60)
    
    # Check if API is running
    try:
        response = requests.get(API_BASE_URL, timeout=10)
        if response.status_code != 200:
            print("❌ API server is not running. Please start the server first.")
            return
    except Exception:
        print("❌ Cannot connect to API server. Please start the server first.")
        return
    
    print("✅ API server is running")
    
    # Check if resume file exists
    if not Path(RESUME_FILE).exists():
        print(f"❌ Resume file not found: {RESUME_FILE}")
        print("Please ensure the resume file exists in the specified path.")
        return
    
    # Parse resume
    print(f"\n📄 Step 1: Parsing Real Resume")
    print("-" * 40)
    resume_data = parse_resume_file(RESUME_FILE)
    
    if not resume_data:
        print("❌ Failed to parse resume. Cannot proceed.")
        return
    
    # Get sample JD data
    jd_data = get_sample_jd_data()
    print(f"\n📋 Step 2: Using Sample Job Description")
    print("-" * 40)
    print(f"   Job Title: {jd_data['job_title']}")
    print(f"   Company: {jd_data['company_name']}")
    print(f"   Location: {jd_data['location']}")
    print(f"   Required Skills: {len(jd_data['required_skills'])} skills")
    print(f"   Preferred Skills: {len(jd_data['preferred_skills'])} skills")
    
    # Test with default weights
    print(f"\n🧪 Step 3: Testing with Default Weights")
    print("-" * 40)
    success1 = test_intervet_new_with_real_resume(resume_data, jd_data, test_name="(Default Weights)")
    
    # Test with skills-heavy weights
    print(f"\n🧪 Step 4: Testing with Skills-Heavy Weights")
    print("-" * 40)
    skills_heavy_weights = {
        "skills": 5.0,
        "experience": 2.0,
        "education": 1.5,
        "certifications": 1.0,
        "location": 0.5,
        "reliability": 0.0
    }
    success2 = test_intervet_new_with_real_resume(resume_data, jd_data, skills_heavy_weights, "(Skills-Heavy)")
    
    # Test with experience-heavy weights
    print(f"\n🧪 Step 5: Testing with Experience-Heavy Weights")
    print("-" * 40)
    experience_heavy_weights = {
        "skills": 2.0,
        "experience": 5.0,
        "education": 2.0,
        "certifications": 1.0,
        "location": 0.0,  # Disable location
        "reliability": 0.0  # Disable reliability
    }
    success3 = test_intervet_new_with_real_resume(resume_data, jd_data, experience_heavy_weights, "(Experience-Heavy)")
    
    # Summary
    print(f"\n📊 TEST SUMMARY")
    print("=" * 60)
    tests_passed = sum([success1, success2, success3])
    print(f"Tests passed: {tests_passed}/3")
    
    if tests_passed == 3:
        print("🎉 All tests passed! The /intervet_new endpoint is working correctly with real resume files.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
    
    print(f"\n📁 Check the 'intervet_new_logs' folder for detailed calculation logs.")
    print(f"📄 Resume used: {RESUME_FILE}")

if __name__ == "__main__":
    main()
