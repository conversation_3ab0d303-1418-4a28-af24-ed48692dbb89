#!/usr/bin/env python3
"""
Test <PERSON><PERSON><PERSON>'s resume with the fixed JSON parsing.
"""

import requests
import json
import time
from pathlib import Path

BASE_URL = "http://localhost:8000"

def test_anurag_with_fixed_parsing():
    """Test <PERSON>ura<PERSON>'s resume with the improved JSON parsing."""
    
    print("🧪 Testing Anurag's Resume with Fixed JSON Parsing")
    print("=" * 70)
    
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print("❌ Server not responding correctly")
            return
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        return
    
    # Test Anurag's resume
    resume_path = Path("resumes for testing/Resume-Anurag Pandey.pdf")
    
    if not resume_path.exists():
        print(f"❌ Resume not found: {resume_path}")
        return
    
    print(f"📄 Testing: {resume_path.name}")
    print(f"📊 File size: {resume_path.stat().st_size:,} bytes")
    
    try:
        # Test the hybrid endpoint
        start_time = time.time()
        
        with open(resume_path, 'rb') as f:
            files = {'file': (resume_path.name, f, 'application/pdf')}
            response = requests.post(f"{BASE_URL}/hybrid_resume", files=files, timeout=180)
        
        processing_time = time.time() - start_time
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"⏱️ Processing Time: {processing_time:.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            
            # Check if there's an error in the response
            if result.get('error'):
                print(f"⚠️ Response contains error: {result['error']}")
                if result.get('error_details'):
                    print(f"   Details: {result['error_details']}")
                return False
            
            print("✅ SUCCESS! JSON parsing worked!")
            
            # Detailed analysis
            print(f"\n📊 Extracted Data:")
            print(f"   👤 Name: '{result.get('name', 'Not found')}'")
            print(f"   📧 Email: {result.get('email', 'None')}")
            print(f"   📞 Phone: {result.get('phone', 'None')}")
            
            # Education
            education = result.get('education', [])
            print(f"   🎓 Education: {len(education)} entries")
            for i, edu in enumerate(education):
                degree = edu.get('degree', 'N/A')
                institution = edu.get('institution', 'N/A')
                year = edu.get('year', 'N/A')
                print(f"      {i+1}. {degree} at {institution} ({year})")
            
            # Skills
            skills = result.get('skills', [])
            print(f"   🛠️ Skills: {len(skills)} items")
            if skills:
                print(f"      {skills}")
            
            # Experience
            experience = result.get('experience', [])
            print(f"   💼 Experience: {len(experience)} entries")
            if experience:
                for i, exp in enumerate(experience):
                    company = exp.get('company_name', 'N/A')
                    role = exp.get('role', 'N/A')
                    duration = exp.get('duration', 'N/A')
                    print(f"      {i+1}. {role} at {company} ({duration})")
            else:
                print(f"      (No professional experience - student resume)")
            
            # Projects
            projects = result.get('projects', [])
            print(f"   🚀 Projects: {len(projects)} items")
            for i, proj in enumerate(projects):
                name = proj.get('name', 'N/A')
                print(f"      {i+1}. {name}")
            
            # Certifications
            certifications = result.get('certifications', [])
            print(f"   🏆 Certifications: {len(certifications)} items")
            for i, cert in enumerate(certifications):
                print(f"      {i+1}. {cert}")
            
            # Achievements
            achievements = result.get('achievements', [])
            print(f"   🏅 Achievements: {len(achievements)} items")
            for i, achievement in enumerate(achievements):
                print(f"      {i+1}. {achievement}")
            
            # Social media
            social_media = result.get('social_media', [])
            print(f"   📱 Social Media: {len(social_media)} items")
            if social_media:
                print(f"      {social_media}")
            
            # Summary
            summary = result.get('summary')
            print(f"   📝 Summary: {'✅ Present' if summary else '❌ Missing'}")
            if summary:
                print(f"      Preview: {summary[:100]}...")
            
            # Verify this matches what we saw in the logs
            expected_data = {
                'name': 'Anurag Pandey',
                'education_count': 3,
                'skills_count': 11,
                'projects_count': 2,
                'certifications_count': 4,
                'achievements_count': 3,
                'has_summary': True
            }
            
            print(f"\n🔍 Verification against log data:")
            actual_data = {
                'name': result.get('name'),
                'education_count': len(result.get('education', [])),
                'skills_count': len(result.get('skills', [])),
                'projects_count': len(result.get('projects', [])),
                'certifications_count': len(result.get('certifications', [])),
                'achievements_count': len(result.get('achievements', [])),
                'has_summary': bool(result.get('summary'))
            }
            
            all_match = True
            for key, expected_value in expected_data.items():
                actual_value = actual_data[key]
                match = actual_value == expected_value
                status = "✅" if match else "❌"
                print(f"   {status} {key}: Expected {expected_value}, Got {actual_value}")
                if not match:
                    all_match = False
            
            if all_match:
                print(f"\n🎉 PERFECT! All data matches the log expectations!")
                print("✅ JSON parsing fix successful")
                print("✅ Data extraction working correctly")
                print("✅ Content classification proper")
            else:
                print(f"\n⚠️ Some data doesn't match expectations")
                print("Check for potential issues in processing")
            
            return all_match
            
        else:
            print("❌ FAILED!")
            print(f"Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    
    success = test_anurag_with_fixed_parsing()
    
    print(f"\n{'='*70}")
    print("📊 TEST SUMMARY")
    print(f"{'='*70}")
    
    if success:
        print("🎉 SUCCESS: JSON parsing fix worked perfectly!")
        print("✅ Anurag's resume data extracted correctly")
        print("✅ All expected data fields present")
        print("✅ Content properly classified")
        print("\n🚀 Ready to test other resumes!")
    else:
        print("❌ Issues still present:")
        print("- Check if JSON parsing is still failing")
        print("- Review the latest prompt logs")
        print("- Consider additional parsing improvements")

if __name__ == "__main__":
    main()
