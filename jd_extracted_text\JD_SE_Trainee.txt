# Extracted Text Debug File
# Source File: JD_SE_Trainee.pdf
# Context: jd
# Extraction Method: pdf_text
# Timestamp: 2025-07-02 15:44:21
# Text Length: 2254 characters
# ================================================

 
Vyoma Linguistic Labs Foundation  
Job Description for Software Engineer Trainee  
 
Position  Software Engineer Trainee  
 
Reporting to  Technology Lead  
 
Job Type  Part-time/ Full-time 
 
 
Company Overview:   
Vyoma Linguistic Labs Foundation is a non-profit organization and eLearning company 
committed to innovation, excellence  and the promotion of Sanskrit within the Indian 
Knowledge System (IKS) through our educational initiatives.   
Responsibilities:  
We are looking out for committed individuals who have a good  attitude & learnability to 
join our team as Software Engineer Trainees. Responsibilities also include gathering user 
requirements, defining system functionality & writing code in various languages like 
HTML, CSS, J avascript  and Media queries . Our ideal candidates are fa miliar with SDLC 
from preliminary system analysis to tests & deployment.  
 Problem solving & Programming - Work closely with the development team to 
troubleshoot, debug, and resolve software defects and issues. Contribute creative 
solutions to technical chal lenges.  Collaborate with senior engineers to design, code, 
and test software applications. Contribute to the development of high -quality, 
scalable, and maintainable code.  
 
 Testing & Quality Assurance  - Assist in the testing process by writing and executing  
test cases. Identify and report bugs and work towards their resolution.  
 

 Documentation - Maintain thorough documentation of code, design, and testing 
activities. Ensure that documentation is accurate, up -to-date, and accessible to team 
members.  
 
 Adaptabil ity & Continuous Learning - Adapt quickly to new technologies and 
programming languages as required by the project. Take initiative in continuous self -
improvement, staying informed about advancements in software development and 
applying new knowledge to yo ur work.  
 
Skills & Qualifications:  
 Bachelor’s degree in IT, Computer Science, Software Engineering, or a related field.  
 Basic understanding of programming languages such as HTML, CSS, Javascript and Media 
Queries . 
 Freshers or 1 year experience.  
 Good written and verbal communication skills in English.  
 Knowledge of Sanskrit would be an added advantage.  
