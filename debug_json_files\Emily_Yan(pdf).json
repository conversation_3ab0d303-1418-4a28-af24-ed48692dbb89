{
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "phone": null,
    "education": [
        {
            "degree": "M.S. in Electrical & Computer Engineering",
            "institution": "Xi’an University of Science and Technology",
            "year": "null"
        },
        {
            "degree": "B.S. in Computer Engineering",
            "institution": "University of Washington",
            "year": "null"
        }
    ],
    "skills": [
        "Java",
        "SQL",
        "Python",
        "JavaScript",
        "RESTful APIs",
        "Microservices",
        "Kafka",
        "RDBMS (Oracle, MySQL, PostgreSQL)",
        "NoSQL (MongoDB, Redis, Cassandra)",
        "Unit Testing",
        "CI/CD",
        "Docker",
        "Kubernetes",
        "Spring Boot",
        "Spring MVC",
        "Spring Data JPA",
        "Hibernate",
        "JUnit",
        "Mockito",
        "React",
        "Splunk",
        "ELK Stack",
        "FastAPI",
        "Pandas",
        "NumPy",
        "Git",
        "Maven",
        "Jenkins",
        "Ansible Tower"
    ],
    "experience": [
        {
            "company_name": "Poshmark",
            "role": "Software Developer, hybrid",
            "duration": "Dec 2023-Present",
            "key_responsibilities": "Worked on enhancing Poshmark’s marketplace platform, focusing on improving key aspects of the shopping and selling experience, aimed to scale the platform, improve real-time features, and streamline the payment and order fulfillment process to support the growing user base, integrate new technologies and the enhance the existing backend systems to ensure efficient data processing, seamless user interactions, and optimized performance Environment: Java, Spring Boot, Spring Security, Hibernate, JPA, RESTful APIs, Apache Kafka, Maven, Jenkins, AWS, Git, MySQL, Database, Kubernetes, Splunk, Hadoop, Python, API Gateway"
        },
        {
            "company_name": "Morningstar",
            "role": "Software Developer, WA",
            "duration": "Sep 2021-Aug 2022",
            "key_responsibilities": "Worked on the Real-Time Virtual Coin Analytics Platform, which is designed to aggregate, process, and deliver real-time data to both institutional and individual investors. The platform integrates data sources, provides advanced analytical tools, and ensures accurate reporting, enabling users to make informed investment decisions. My role focused on building scalable, high-performance backend services and ensuring the platform could handle a high volume of data and user interactions Environment: Java, Spring Boot, Spring Security, Hibernate, JPA, RESTful APIs, WebSocket, Apache Kafka, Maven, Jenkins, AWS, Tableau Git, Oracle Database, Python, GraphQL, gRPC, Openshift"
        },
        {
            "company_name": "Baidu USA",
            "role": "Software Developer, CA",
            "duration": "Apr 2017-May 2021",
            "key_responsibilities": "I was part of a cross-functional team responsible for enhancing Baidu’s cloud-based AI and big data infrastructure. We developed and optimized scalable, real-time data processing systems to support AI-driven applications, enabling efficient data ingestion, analysis, and real-time insights for autonomous driving, smart city solutions, and enterprise AI services Environment: Java, Spring Boot, Spring Security, Hibernate, JPA, RESTful APIs, WebSocket, Apache Kafka, Flink, Kubernetes, Docker Maven, Jenkins, AWS/GCP, Git, Ansible Tower, MySQL, Redis"
        }
    ],
    "projects": [
        {
            "name": "Poshmark Marketplace Enhancement",
            "description": "Enhanced Poshmark’s marketplace platform, focusing on improving key aspects of the shopping and selling experience, aiming to scale the platform, improve real-time features, and streamline the payment and order fulfillment process to support the growing user base, integrated new technologies and the enhance the existing backend systems to ensure efficient data processing, seamless user interactions, and optimized performance"
        }