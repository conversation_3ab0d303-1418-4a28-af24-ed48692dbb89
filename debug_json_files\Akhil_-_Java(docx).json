{
  "experience": [
    {
      "title": "Java/J2EE Developer",
      "company": "Payism",
      "location": "Bangalore, India",
      "dates": "Jun 2013 – Nov 2015",
      "responsibilities": [
        "Developed various generic JavaScript functions used for validations.",
        "Developed screens using JSP, JavaScript, AJAX, and Ext JS.",
        "Used AJAX extensively to implement front end /user interface features in the application.",
        "Developed Web Services clients to consume those Web Services as well as other enterprise-wide Web Services.",
        "Exposed the Web Services to the client applications by sharing the WSDL.",
        "Created logical and physical data models putting to practice, concepts of normalization and RDBMS.",
        "Developed various generic JavaScript functions used for validations.",
        "Developed screens using JSP, JavaScript, AJAX, and Ext JS.",
        "Used AJAX extensively to implement front end /user interface features in the application.",
        "Developed Web Services clients to consume those Web Services as well as other enterprise-wide Web Services.",
        "Exposed the Web Services to the client applications by sharing the WSDL."
      ]
    },
    {
      "title": "Java/J2EE Developer",
      "company": "Merck",
      "location": "North Wales, PA",
      "dates": "Sep 2017 – Apr 2019",
      "responsibilities": [
        "Worked in all the modules of the application which involved front-end presentation logic developed using Tiles, JSP, JSTL, and JavaScript, Business objects developed using POJOs and data access layer using hibernate framework.",
        "Developed various generic JavaScript functions used for validations.",
        "Developed screens using JSP, JavaScript, AJAX, and Ext JS.",
        "Used AJAX extensively to implement front end /user interface features in the application.",
        "Developed Web Services clients to consume those Web Services as well as other enterprise-wide Web Services.",
        "Exposed the Web Services to the client applications by sharing the WSDL.",
        "Created logical and physical data models putting to practice, concepts of normalization and RDBMS.",
        "Developed various generic JavaScript functions used for validations.",
        "Developed screens using JSP, JavaScript, AJAX, and Ext JS.",
        "Used AJAX extensively to implement front end /user interface features in the application.",
        "Developed Web Services clients to consume those Web Services as well as other enterprise-wide Web Services.",
        "Exposed the Web Services to the client applications by sharing the WSDL.",
        "Created logical and physical data models putting to practice, concepts of normalization and RDBMS."
      ]
    },
    {
      "title": "Java/J2EE Developer",
      "company": "Payism",
      "location": "Bangalore, India",
      "dates": "Jun 2013 – Nov 2015",
      "responsibilities": [
        "Worked in all the modules of the application which involved front-end presentation logic developed using Tiles, JSP, JSTL, and JavaScript, Business objects developed using POJOs and data access layer using hibernate framework.",
        "Developed various generic JavaScript functions used for validations.",
        "Developed screens using JSP, JavaScript, AJAX, and Ext JS.",
        "Used AJAX extensively to implement front end /user interface features in the application.",
        "Developed Web Services clients to consume those Web Services as well as other enterprise-wide Web Services.",
        "Exposed the Web Services to the client applications by sharing the WSDL.",
        "Created logical and physical data models putting to practice, concepts of normalization and RDBMS.",
        "Developed various generic JavaScript functions used for validations.",
        "Developed screens using JSP, JavaScript, AJAX, and Ext JS.",
        "Used AJAX extensively to implement front end /user interface features in the application.",
        "Developed Web Services clients to consume those Web Services as well as other enterprise-wide Web Services.",
        "Exposed the Web Services to the client applications by sharing the WSDL."
      ]
    }
  ],
  "education": [
    {
      "degree": "Bachelor of Technology in Computer Science",
      "institution": "TKR College of Engineering and Technology",
      "year": 2013
    },
    {
      "degree": "Master’s in CS",
      "institution": "University of Dayton",
      "year": 2017
    }