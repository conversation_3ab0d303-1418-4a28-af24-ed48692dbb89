{
  "skills": [
    "Java",
    "J2<PERSON>",
    "Spring Boot",
    "Spring MVC",
    "Hibernate",
    "AngularJS",
    "Angular 6/7/8/9/10",
    "Drools",
    "Vert.x",
    "RESTful APIs",
    "SQL",
    "Oracle",
    "MongoDB",
    "MySQL",
    "J2EE",
    "HTML",
    "CSS",
    "JavaScript",
    "JSON",
    "XML",
    "RESTful APIs",
    "Web Services",
    "Agile",
    "J2EE",
    "Spring",
    "Maven",
    "Gradle",
    "Git",
    "Jenkins",
    "Docker",
    "Kubernetes",
    "AWS",
    "Azure",
    "GCP",
    "SQL",
    "NoSQL",
    "Microservices",
    "Cloud Environments",
    "Web Services",
    "J2EE",
    "Spring",
    "Hibernate",
    "AngularJS",
    "Angular",
    "REST",
    "SQL",
    "NoSQL",
    "Agile",
    "DevOps",
    "Testing"
  ],
  "experience": [
    {
      "title": "Sr. Java Backend Developer",
      "company": "CSC",
      "years": "2018 - 2019",
      "responsibilities": [
        "Developed REST APIs to support React JS for creating new UI portal-based development.",
        "Designed and developed End Points (Controllers), Business Layer, DAO Layer using Hibernate/JDBC templates, using Spring IOC (Dependency Injection).",
        "Utilized Angular 6 framework to bind HTML template (views) to JavaScript object (models).",
        "Implemented Spring MVC architecture and increased modularity by allowing the separation of cross-cutting concerns using Spring AOP.",
        "Used Apache Kafka for streaming real-time data pipelines and streaming of application data to achieve asynchronous messaging.",
        "Built custom drawing tools and data visualizations with Canvas API, including real-time rendering and user interaction features.",
        "Designed responsive and scalable Canvas elements compatible with various screen sizes and devices.",
        "Designed, built and deployed application using the AWS stack (Including EC2, Mongo DB, Docker, ",
        "Extensively worked on writing complex PL/SQL Queries using joins, stored procedures, Functions, ",
        "Test automation for web application using Cucumber.",
        "Experience in Developing User Interface (UI) Rich Web Applications and Web Service Applications using HTML 4, XHTML, CSS 2, XML, AJAX, Object Oriented Java Script, ANGULARJS, REACTJS, BOOTSTRAP Framework, RESTful services, JAVA, JSP,",
        "Apache Tomcat Serve is used for deploying the projects.",
        "Used WebLogic fuse for remote console login, JMX management and web server console."
      ]
    }