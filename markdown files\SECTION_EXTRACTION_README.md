# Resume Section Extraction

This document describes the resume section extraction functionality designed to test the Gemma model's capabilities for isolating and extracting specific sections from resume documents.

## Overview

The section extraction system provides four different approaches to extract and parse resume information:

1. **Multiple Calls Method** (`/section`) - Makes individual LLM calls for each section
2. **Single Call Method** (`/section2`) - Extracts all sections in one comprehensive call
3. **Regex Pattern Matching** (`/section3`) - Uses regex patterns to identify and extract sections (fastest, no LLM calls)
4. **🆕 Hybrid Approach** (`/hybrid_resume`) - Combines regex section extraction with LLM JSON structuring

All methods extract the following sections:
- **🆕 Basic Info** - Contact information and role (name, email, phone, job title)
- **Summary/Objective** - Professional summary or career objective
- **Education** - Educational background and qualifications
- **Experience** - Work experience and employment history
- **Skills** - Technical and soft skills
- **Projects** - Personal or professional projects
- **Certifications** - Professional certifications and licenses
- **Achievements** - Awards, honors, and accomplishments
- **Languages** - Language skills and proficiency levels

### 🧠 Smart Basic Info Extraction

The system now includes intelligent extraction of contact information and role that typically appears at the top of resumes without explicit section headers:

- **Automatic Header Detection**: Identifies where basic info ends and formal sections begin
- **Pattern Recognition**: Uses regex patterns to extract email, phone numbers, and job titles
- **Name Extraction**: Intelligently identifies the candidate's name from the header
- **Role Detection**: Finds job titles using keyword matching (developer, engineer, manager, etc.)
- **Individual Components**: Provides separate access to `basic_name`, `basic_email`, `basic_phone`, `basic_role`
- **High Confidence Scoring**: Calculates confidence based on pattern matches and content quality
- **Format Flexibility**: Works with various header layouts (traditional, compact, social links included)

## API Endpoints

### POST /section (Multiple Calls Method)

Extracts resume sections using individual LLM calls for each section.

**Request:**
```http
POST /section
Content-Type: multipart/form-data

file: [Resume file - PDF or DOCX]
```

**Response:**
```json
{
  "filename": "john_doe_resume.pdf",
  "extraction_method": "multiple_calls",
  "sections_extracted": {
    "summary": "Experienced software engineer with 5+ years...",
    "education": "Bachelor of Science in Computer Science...",
    "experience": "Senior Software Engineer at TechCorp...",
    "skills": "Python, JavaScript, React, AWS...",
    "projects": "E-commerce Platform - Built a full-stack...",
    "certifications": "AWS Certified Solutions Architect...",
    "achievements": "Winner of TechCorp Hackathon 2023...",
    "languages": "English: Native, Spanish: Conversational..."
  },
  "extraction_stats": {
    "processing_time": 45.67,
    "total_calls": 8,
    "overall_confidence": 0.85,
    "text_length": 2345,
    "sections_found": 7
  },
  "confidence_scores": {
    "summary": 0.9,
    "education": 0.85,
    "experience": 0.92,
    "skills": 0.88,
    "projects": 0.75,
    "certifications": 0.8,
    "achievements": 0.7,
    "languages": 0.6
  },
  "overall_confidence": 0.85,
  "processing_time": 45.67,
  "errors": []
}
```

### POST /section2 (Single Call Method)

Extracts all resume sections using a single comprehensive LLM call.

**Request:**
```http
POST /section2
Content-Type: multipart/form-data

file: [Resume file - PDF or DOCX]
```

**Response:**
```json
{
  "filename": "john_doe_resume.pdf",
  "extraction_method": "single_call",
  "sections_extracted": {
    "summary": "Experienced software engineer with 5+ years...",
    "education": "Bachelor of Science in Computer Science...",
    "experience": "Senior Software Engineer at TechCorp...",
    "skills": "Python, JavaScript, React, AWS...",
    "projects": "E-commerce Platform - Built a full-stack...",
    "certifications": "AWS Certified Solutions Architect...",
    "achievements": "Winner of TechCorp Hackathon 2023...",
    "languages": "English: Native, Spanish: Conversational..."
  },
  "extraction_stats": {
    "processing_time": 15.23,
    "total_calls": 1,
    "overall_confidence": 0.78,
    "text_length": 2345,
    "sections_found": 6
  },
  "confidence_scores": {
    "summary": 0.8,
    "education": 0.75,
    "experience": 0.85,
    "skills": 0.8,
    "projects": 0.7,
    "certifications": 0.75,
    "achievements": 0.65,
    "languages": 0.5
  },
  "overall_confidence": 0.78,
  "processing_time": 15.23,
  "errors": []
}
```

### POST /section3 (Regex Pattern Matching)

Extracts resume sections using regex pattern matching to identify section headers and extract content between them.

**Request:**
```http
POST /section3
Content-Type: multipart/form-data

file: [Resume file - PDF or DOCX]
```

**Response:**
```json
{
  "filename": "john_doe_resume.pdf",
  "extraction_method": "regex",
  "sections_extracted": {
    "basic_info": "John Doe\nSenior Software Engineer\nEmail: <EMAIL>\nPhone: (*************",
    "basic_name": "John Doe",
    "basic_email": "<EMAIL>",
    "basic_phone": "(*************",
    "basic_role": "Senior Software Engineer",
    "summary": "Experienced software engineer with 5+ years...",
    "education": "Bachelor of Science in Computer Science...",
    "experience": "Senior Software Engineer at TechCorp...",
    "skills": "Python, JavaScript, React, AWS...",
    "projects": "E-commerce Platform - Built a full-stack...",
    "certifications": "AWS Certified Solutions Architect...",
    "achievements": "Winner of TechCorp Hackathon 2023...",
    "languages": "English: Native, Spanish: Conversational..."
  },
  "extraction_stats": {
    "processing_time": 2.15,
    "total_calls": 0,
    "overall_confidence": 0.72,
    "text_length": 2345,
    "sections_found": 6,
    "extraction_method": "regex_pattern_matching"
  },
  "confidence_scores": {
    "basic_info": 1.0,
    "basic_name": 0.85,
    "basic_email": 0.95,
    "basic_phone": 0.9,
    "basic_role": 0.8,
    "summary": 0.75,
    "education": 0.8,
    "experience": 0.85,
    "skills": 0.6,
    "projects": 0.7,
    "certifications": 0.75,
    "achievements": 0.65,
    "languages": 0.5
  },
  "overall_confidence": 0.72,
  "processing_time": 2.15,
  "errors": []
}
```

### POST /hybrid_resume (Hybrid Approach) 🆕

Combines regex section extraction with LLM JSON structuring for optimal speed/accuracy balance.

**Request:**
```http
POST /hybrid_resume
Content-Type: multipart/form-data

file: [Resume file - PDF or DOCX]
```

**Response:**
```json
{
  "name": "Aabhas Fulzele",
  "email": "<EMAIL>",
  "phone": "+91 8381010425",
  "education": [
    {
      "degree": "M. Tech, Software Engineering",
      "institution": "Veermata Jijabai Technological Institute, Mumbai",
      "year": "Feb 2021 - Oct2022"
    },
    {
      "degree": "B.E, Computer Engineering",
      "institution": "Maharashtra Institute of Technology, Pune",
      "year": "2016 – 2020"
    }
  ],
  "skills": {
    "VB.NET": "Programming Languages",
    "C#": "Programming Languages",
    "Python": "Programming Languages",
    "ASP.NET": "Frameworks",
    "Docker": "DevOps Tools",
    "AWS": "Cloud Platforms"
  },
  "experience": [
    {
      "company_name": "National Securities Depository Limited",
      "role": "Junior Software Engineer",
      "duration": "JAN 2023 - present",
      "key_responsibilities": "Used vb.net for coding. Used Itextsharp to generate reports. Involved in documentation of the components and reporting"
    }
  ],
  "projects": [
    {
      "name": "CI/CD pipeline implementation using Jenkins",
      "description": "Implemented CI/CD pipeline using AWS. Responsible for Docker container creation and management, Docker file management, deployment of micro services to container."
    }
  ],
  "certifications": [
    "AWS Certified Cloud Practitioner",
    "AWS re/start graduate"
  ],
  "processing_time": 13.88,
  "extraction_method": "hybrid_regex_llm",
  "sections_extracted": 9,
  "regex_confidence": 0.94,
  "confidence_score": 0.76
}
```

## Output Files

All extraction results are automatically saved to the `resume sections extracted` directory with the following naming convention:

```
{timestamp}_{filename}_{method}_sections.txt
```

Example: `20241217_john_doe_resume_multiple_calls_sections.txt`

### File Format

```
================================================================================
RESUME SECTION EXTRACTION RESULTS
================================================================================
Source File: john_doe_resume.pdf
Extraction Method: multiple_calls
Timestamp: 2024-12-17T14:30:52.123456
Total Sections Extracted: 7
Processing Time: 45.67 seconds
Total LLM Calls: 8
Overall Confidence: 0.85
================================================================================

[SUMMARY]
----------------------------------------
Experienced software engineer with 5+ years of expertise in full-stack 
development, machine learning, and cloud technologies. Proven track record 
of delivering scalable solutions and leading cross-functional teams.

========================================

[EDUCATION]
----------------------------------------
Bachelor of Science in Computer Science
University of California, Berkeley
2016-2020
GPA: 3.8/4.0
Relevant Coursework: Data Structures, Algorithms, Machine Learning

========================================

[EXPERIENCE]
----------------------------------------
Senior Software Engineer
TechCorp Inc., San Francisco, CA
January 2022 - Present
• Led development of microservices architecture serving 1M+ users
• Implemented machine learning models for recommendation systems

========================================

... (continues for all sections)
```

## Method Comparison

### Multiple Calls Method (/section)

**Advantages:**
- ✅ More focused prompts per section
- ✅ Individual confidence scores for each section
- ✅ Better error isolation and handling
- ✅ Potentially higher accuracy per section
- ✅ Detailed error reporting

**Disadvantages:**
- ❌ More API calls (higher cost)
- ❌ Longer processing time
- ❌ More complex error handling
- ❌ Higher token usage

**Best for:** High-accuracy requirements, detailed analysis, debugging

### Single Call Method (/section2)

**Advantages:**
- ✅ Faster processing (single call)
- ✅ Lower cost and token usage
- ✅ Simpler implementation
- ✅ Context awareness across sections
- ✅ Consistent formatting

**Disadvantages:**
- ❌ May miss some sections
- ❌ Less focused extraction
- ❌ Harder to debug failures
- ❌ Lower individual section accuracy

**Best for:** Quick analysis, cost efficiency, batch processing

### Regex Pattern Matching (/section3)

**Advantages:**
- ✅ Fastest processing (no LLM calls)
- ✅ Zero cost for extraction (no API calls)
- ✅ Consistent and deterministic results
- ✅ Works well with well-formatted resumes
- ✅ No token usage or rate limits
- ✅ Immediate results

**Disadvantages:**
- ❌ Less flexible with varied formatting
- ❌ May miss sections with unusual headers
- ❌ Requires clear section boundaries
- ❌ Cannot understand context or semantics
- ❌ May struggle with creative section names

**Best for:** High-volume processing, cost-sensitive applications, well-formatted resumes

### 🆕 Hybrid Approach (/hybrid_resume)

**Advantages:**
- ✅ Faster than pure LLM (pre-structured sections reduce processing)
- ✅ More accurate than pure regex (LLM handles JSON structuring)
- ✅ Cost-effective (reduced token usage vs pure LLM)
- ✅ Structured JSON output (like /resume endpoint)
- ✅ Reliable section detection (regex handles boundaries well)
- ✅ Intelligent parsing (LLM handles context and relationships)
- ✅ Best of both worlds (speed + accuracy + cost efficiency)

**Disadvantages:**
- ❌ Slower than pure regex (includes LLM processing)
- ❌ More expensive than pure regex (but cheaper than pure LLM)
- ❌ Still requires well-formatted section headers for optimal results
- ❌ Two-step process (more complex than single methods)

**Best for:** Balanced production workloads, medium-volume processing, applications needing structured JSON with good performance

## Testing

### Using the Test Scripts

**Test all methods:**
```bash
cd testing
python test_section_extraction.py
```

**Test regex method specifically:**
```bash
cd testing
python test_section3_regex.py
```

**Test basic info extraction:**
```bash
python test_basic_info_extraction.py
```

**Test hybrid approach:**
```bash
python test_hybrid_resume.py
python test_hybrid_api.py
```

These scripts will:
1. Test all four endpoints with available resume files
2. Compare processing times and accuracy
3. Show detailed section extraction results
4. Analyze confidence scores
5. Compare method performance
6. Test basic info extraction with various header formats
7. Test hybrid regex + LLM approach

### Using the Demo Script

```bash
python demo_section_extraction.py
```

This script demonstrates the functionality with sample resume text.

## Confidence Scoring

The system provides confidence scores for each extracted section:

- **0.9-1.0**: High confidence - Section clearly identified and extracted
- **0.7-0.8**: Good confidence - Section found with minor uncertainties
- **0.5-0.6**: Medium confidence - Section partially identified
- **0.0-0.4**: Low confidence - Section unclear or not found

Factors affecting confidence:
- Content length and structure
- Presence of section keywords
- Text quality and formatting
- Model response consistency

## Error Handling

The system handles various error scenarios:

- **File format errors**: Invalid PDF/DOCX files
- **Text extraction failures**: Corrupted or image-only files
- **LLM timeout errors**: Long processing times
- **Section parsing errors**: Malformed responses
- **Individual section failures**: Isolated extraction errors

## Best Practices

1. **File Quality**: Use high-quality, text-based PDF/DOCX files
2. **File Size**: Keep files under 10MB for optimal performance
3. **Method Selection**:
   - Use `/section` for accuracy-critical applications
   - Use `/section2` for speed and cost efficiency
   - Use `/section3` for maximum speed and zero cost (well-formatted resumes)
   - Use `/hybrid_resume` for balanced speed/accuracy/cost with structured JSON output
4. **Error Monitoring**: Check confidence scores and error arrays
5. **Result Validation**: Review extracted sections for accuracy

## Troubleshooting

### Common Issues

1. **Empty sections**: Check if the resume actually contains those sections
2. **Low confidence**: May indicate poor text quality or unusual formatting
3. **Timeout errors**: Large files or complex layouts may need longer processing
4. **Missing sections**: Some resumes may not have all standard sections

### Performance Optimization

- Use appropriate timeout values based on file size
- Monitor token usage for cost optimization
- Implement caching for repeated extractions
- Consider preprocessing for better text quality

## Integration Examples

### Python Client

```python
import requests

# Test multiple calls method
with open('resume.pdf', 'rb') as f:
    files = {'file': ('resume.pdf', f, 'application/pdf')}
    response = requests.post('http://localhost:8000/section', files=files)
    result = response.json()

print(f"Sections found: {result['extraction_stats']['sections_found']}")
for section, content in result['sections_extracted'].items():
    if content and content.strip():
        print(f"{section}: {content[:100]}...")
```

### cURL Example

```bash
curl -X POST "http://localhost:8000/section" \
     -F "file=@resume.pdf" \
     -H "accept: application/json"
```

This section extraction system provides comprehensive testing capabilities for evaluating the Gemma model's performance on resume parsing tasks, with detailed logging and analysis features.
