#!/usr/bin/env python3
"""
Advanced test script for the /interfix endpoint with edge cases
"""

import requests
import json

# API endpoint
BASE_URL = "http://localhost:8000"
INTERFIX_URL = f"{BASE_URL}/interfix"

def test_advanced_cases():
    """Test the /interfix endpoint with advanced and edge cases"""
    
    # Test case 1: Different salary formats
    test_case_1 = {
        "summary": """
        The candidate mentioned they are expecting around 15 LPA (15 lakhs per annum) as their salary.
        They have a current offer of 12 lakhs annually. Their notice period is immediate as they are currently unemployed.
        They want to switch because they are looking for better work-life balance.
        They prefer afternoon interviews, specifically around 2:00 PM, and are available on 2024-12-15.
        """
    }
    
    # Test case 2: Multiple salary mentions
    test_case_2 = {
        "summary": """
        During the call, the candidate first mentioned they expect 80k per month, but later clarified 
        that they would be happy with 75000 monthly. They have a 3-month notice period.
        The main reason for job change is career growth and learning new technologies.
        They are flexible with interview timing but prefer mornings between 9 AM to 11 AM.
        """
    }
    
    # Test case 3: Ambiguous information
    test_case_3 = {
        "summary": """
        The candidate said they might have some offers but didn't specify amounts.
        They mentioned something about a notice period but it wasn't clear.
        They want to change jobs for personal reasons which they didn't elaborate.
        Interview timing was discussed but no specific preference was given.
        """
    }
    
    # Test case 4: Very detailed information
    test_case_4 = {
        "summary": """
        Comprehensive screening call conducted. Candidate details:
        - Current offer in hand: 95,000 rupees per month from TCS
        - Notice period: 60 days as per company policy
        - Expected salary: Looking for 1.2 lakhs monthly (120,000 INR)
        - Reason for switch: Current role lacks growth opportunities and wants to work on AI/ML projects
        - Interview preferences: Available for interviews on weekdays, prefers 10:30 AM slot
        - Specific dates: Available from January 15th, 2024 onwards
        - Additional note: Can be flexible with timing if needed
        """
    }
    
    # Test case 5: Empty/minimal summary
    test_case_5 = {
        "summary": "Call completed. Candidate seems interested."
    }
    
    test_cases = [
        ("Different Salary Formats", test_case_1),
        ("Multiple Salary Mentions", test_case_2),
        ("Ambiguous Information", test_case_3),
        ("Very Detailed Information", test_case_4),
        ("Empty/Minimal Summary", test_case_5)
    ]
    
    print("Testing /interfix endpoint with advanced cases...")
    print("=" * 60)
    
    for test_name, test_data in test_cases:
        print(f"\n🧪 Test Case: {test_name}")
        print("-" * 40)
        print(f"Input Summary: {test_data['summary'][:100]}...")
        
        try:
            # Make the API request
            response = requests.post(
                INTERFIX_URL,
                json=test_data,
                headers={"Content-Type": "application/json"},
                timeout=120  # 2 minute timeout for AI processing
            )
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Success! Extracted information:")
                
                # Pretty print with analysis
                for key, value in result.items():
                    if value is not None:
                        print(f"  ✓ {key}: {value}")
                    else:
                        print(f"  ○ {key}: null")
                        
            else:
                print(f"❌ Error: {response.status_code}")
                print(f"Response: {response.text}")
                
        except requests.exceptions.Timeout:
            print("❌ Request timed out")
        except requests.exceptions.ConnectionError:
            print("❌ Could not connect to the API server")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
    
    print("\n" + "=" * 60)
    print("Advanced testing completed!")

def test_error_cases():
    """Test error handling"""
    print("\n🚨 Testing error cases...")
    print("-" * 30)
    
    # Test case 1: Missing summary field
    try:
        response = requests.post(
            INTERFIX_URL,
            json={},  # Missing summary field
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        print(f"Missing summary field - Status: {response.status_code}")
        if response.status_code != 200:
            print("✅ Correctly handled missing field")
        else:
            print("❌ Should have returned an error")
    except Exception as e:
        print(f"Error testing missing field: {e}")
    
    # Test case 2: Invalid JSON
    try:
        response = requests.post(
            INTERFIX_URL,
            data="invalid json",
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        print(f"Invalid JSON - Status: {response.status_code}")
        if response.status_code != 200:
            print("✅ Correctly handled invalid JSON")
        else:
            print("❌ Should have returned an error")
    except Exception as e:
        print(f"Error testing invalid JSON: {e}")

if __name__ == "__main__":
    # Test advanced cases
    test_advanced_cases()
    
    # Test error handling
    test_error_cases()
