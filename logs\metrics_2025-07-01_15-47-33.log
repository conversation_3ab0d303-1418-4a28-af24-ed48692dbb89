{"event": "session_start", "session_id": "dc5a0d23-d2fc-4624-b539-2e2a84dcea7d", "timestamp": "2025-07-01T15:47:33.283949", "message": "New API session started"}
{"event": "request_start", "session_id": "dc5a0d23-d2fc-4624-b539-2e2a84dcea7d", "request_id": "45d9d703-757a-462c-bc96-3a89526c9bb9", "endpoint": "/", "timestamp": "2025-07-01T15:48:40.629596", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "dc5a0d23-d2fc-4624-b539-2e2a84dcea7d", "request_id": "45d9d703-757a-462c-bc96-3a89526c9bb9", "endpoint": "/", "timestamp": "2025-07-01T15:48:40.630596", "total_time_seconds": 0.00099945068359375, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "dc5a0d23-d2fc-4624-b539-2e2a84dcea7d", "request_id": "b01c6495-6aef-421f-b8f2-58ad1e392d2a", "endpoint": "/resume", "timestamp": "2025-07-01T15:48:42.660212", "message": "Request started for endpoint: /resume"}
{"event": "custom_metric", "session_id": "dc5a0d23-d2fc-4624-b539-2e2a84dcea7d", "request_id": "b01c6495-6aef-421f-b8f2-58ad1e392d2a", "endpoint": "resume", "timestamp": "2025-07-01T15:48:42.662209", "message": "Custom metric: endpoint=resume"}
{"event": "custom_metric", "session_id": "dc5a0d23-d2fc-4624-b539-2e2a84dcea7d", "request_id": "b01c6495-6aef-421f-b8f2-58ad1e392d2a", "endpoint": "/resume", "timestamp": "2025-07-01T15:48:42.662209", "file_name": "Resume-Raman Luhach.pdf", "message": "Custom metric: file_name=Resume-Raman Luhach.pdf"}
{"event": "custom_metric", "session_id": "dc5a0d23-d2fc-4624-b539-2e2a84dcea7d", "request_id": "b01c6495-6aef-421f-b8f2-58ad1e392d2a", "endpoint": "/resume", "timestamp": "2025-07-01T15:48:42.662209", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "dc5a0d23-d2fc-4624-b539-2e2a84dcea7d", "request_id": "b01c6495-6aef-421f-b8f2-58ad1e392d2a", "endpoint": "/resume", "timestamp": "2025-07-01T15:48:42.664209", "file_size": 73845, "message": "Custom metric: file_size=73845"}
{"event": "custom_metric", "session_id": "dc5a0d23-d2fc-4624-b539-2e2a84dcea7d", "request_id": "b01c6495-6aef-421f-b8f2-58ad1e392d2a", "endpoint": "/resume", "timestamp": "2025-07-01T15:48:42.679266", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "dc5a0d23-d2fc-4624-b539-2e2a84dcea7d", "request_id": "b01c6495-6aef-421f-b8f2-58ad1e392d2a", "endpoint": "/resume", "timestamp": "2025-07-01T15:48:42.679266", "file_size_bytes": 73845, "message": "Custom metric: file_size_bytes=73845"}
{"event": "custom_metric", "session_id": "dc5a0d23-d2fc-4624-b539-2e2a84dcea7d", "request_id": "b01c6495-6aef-421f-b8f2-58ad1e392d2a", "endpoint": "/resume", "timestamp": "2025-07-01T15:48:42.679266", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "dc5a0d23-d2fc-4624-b539-2e2a84dcea7d", "request_id": "b01c6495-6aef-421f-b8f2-58ad1e392d2a", "endpoint": "/resume", "timestamp": "2025-07-01T15:48:42.679266", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "dc5a0d23-d2fc-4624-b539-2e2a84dcea7d", "request_id": "b01c6495-6aef-421f-b8f2-58ad1e392d2a", "endpoint": "/resume", "timestamp": "2025-07-01T15:48:42.679266", "file_processing_time": 0.015056848526000977, "message": "Custom metric: file_processing_time=0.015056848526000977"}
{"event": "custom_metric", "session_id": "dc5a0d23-d2fc-4624-b539-2e2a84dcea7d", "request_id": "b01c6495-6aef-421f-b8f2-58ad1e392d2a", "endpoint": "/resume", "timestamp": "2025-07-01T15:48:42.679266", "text_extraction_time": 0.015056848526000977, "message": "Custom metric: text_extraction_time=0.015056848526000977"}
{"event": "custom_metric", "session_id": "dc5a0d23-d2fc-4624-b539-2e2a84dcea7d", "request_id": "b01c6495-6aef-421f-b8f2-58ad1e392d2a", "endpoint": "/resume", "timestamp": "2025-07-01T15:48:42.679266", "extracted_text_length": 2214, "message": "Custom metric: extracted_text_length=2214"}
{"event": "custom_metric", "session_id": "dc5a0d23-d2fc-4624-b539-2e2a84dcea7d", "request_id": "b01c6495-6aef-421f-b8f2-58ad1e392d2a", "endpoint": "/resume", "timestamp": "2025-07-01T15:48:59.817125", "parsing_time": 17.136860370635986, "message": "Custom metric: parsing_time=17.136860370635986"}
{"event": "custom_metric", "session_id": "dc5a0d23-d2fc-4624-b539-2e2a84dcea7d", "request_id": "b01c6495-6aef-421f-b8f2-58ad1e392d2a", "endpoint": "/resume", "timestamp": "2025-07-01T15:48:59.817125", "confidence_score": 0.55, "message": "Custom metric: confidence_score=0.55"}
{"event": "custom_metric", "session_id": "dc5a0d23-d2fc-4624-b539-2e2a84dcea7d", "request_id": "b01c6495-6aef-421f-b8f2-58ad1e392d2a", "endpoint": "/resume", "timestamp": "2025-07-01T15:48:59.817125", "fields_extracted": 18, "message": "Custom metric: fields_extracted=18"}
{"event": "custom_metric", "session_id": "dc5a0d23-d2fc-4624-b539-2e2a84dcea7d", "request_id": "b01c6495-6aef-421f-b8f2-58ad1e392d2a", "endpoint": "/resume", "timestamp": "2025-07-01T15:48:59.817125", "skills_count": 12, "message": "Custom metric: skills_count=12"}
{"event": "custom_metric", "session_id": "dc5a0d23-d2fc-4624-b539-2e2a84dcea7d", "request_id": "b01c6495-6aef-421f-b8f2-58ad1e392d2a", "endpoint": "/resume", "timestamp": "2025-07-01T15:48:59.817125", "education_count": 3, "message": "Custom metric: education_count=3"}
{"event": "custom_metric", "session_id": "dc5a0d23-d2fc-4624-b539-2e2a84dcea7d", "request_id": "b01c6495-6aef-421f-b8f2-58ad1e392d2a", "endpoint": "/resume", "timestamp": "2025-07-01T15:48:59.817125", "experience_count": 0, "message": "Custom metric: experience_count=0"}
{"event": "custom_metric", "session_id": "dc5a0d23-d2fc-4624-b539-2e2a84dcea7d", "request_id": "b01c6495-6aef-421f-b8f2-58ad1e392d2a", "endpoint": "/resume", "timestamp": "2025-07-01T15:48:59.818123", "total_processing_time": 17.151917219161987, "message": "Custom metric: total_processing_time=17.151917219161987"}
{"event": "request_complete", "session_id": "dc5a0d23-d2fc-4624-b539-2e2a84dcea7d", "request_id": "b01c6495-6aef-421f-b8f2-58ad1e392d2a", "endpoint": "/resume", "timestamp": "2025-07-01T15:48:59.820122", "total_time_seconds": 17.15990948677063, "status_code": 200, "message": "Request completed in 17.1599s with status 200"}
{"event": "request_start", "session_id": "dc5a0d23-d2fc-4624-b539-2e2a84dcea7d", "request_id": "3f305305-c35c-406b-86d5-b46d8fc2633b", "endpoint": "/jd_parser", "timestamp": "2025-07-01T15:49:01.850719", "message": "Request started for endpoint: /jd_parser"}
{"event": "request_complete", "session_id": "dc5a0d23-d2fc-4624-b539-2e2a84dcea7d", "request_id": "3f305305-c35c-406b-86d5-b46d8fc2633b", "endpoint": "/jd_parser", "timestamp": "2025-07-01T15:49:01.852719", "total_time_seconds": 0.0019998550415039062, "status_code": 400, "message": "Request completed in 0.0020s with status 400"}
{"event": "session_end", "session_id": "dc5a0d23-d2fc-4624-b539-2e2a84dcea7d", "timestamp": "2025-07-01T15:52:32.744485", "message": "API session ended"}
