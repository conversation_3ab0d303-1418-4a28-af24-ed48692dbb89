{"event": "session_start", "session_id": "797e739d-0e54-45a9-90b3-ac1ed4f0f814", "timestamp": "2025-07-01T17:31:54.661248", "message": "New API session started"}
{"event": "request_start", "session_id": "797e739d-0e54-45a9-90b3-ac1ed4f0f814", "request_id": "45273a7c-9575-43d9-a91d-7335e42ed5bb", "endpoint": "/", "timestamp": "2025-07-01T17:31:56.043661", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "797e739d-0e54-45a9-90b3-ac1ed4f0f814", "request_id": "45273a7c-9575-43d9-a91d-7335e42ed5bb", "endpoint": "/", "timestamp": "2025-07-01T17:31:56.045972", "total_time_seconds": 0.002311229705810547, "status_code": 200, "message": "Request completed in 0.0023s with status 200"}
{"event": "request_start", "session_id": "797e739d-0e54-45a9-90b3-ac1ed4f0f814", "request_id": "d59901e8-224e-4fcc-bbea-ffaf174f328c", "endpoint": "/favicon.ico", "timestamp": "2025-07-01T17:31:56.116289", "message": "Request started for endpoint: /favicon.ico"}
{"event": "request_complete", "session_id": "797e739d-0e54-45a9-90b3-ac1ed4f0f814", "request_id": "d59901e8-224e-4fcc-bbea-ffaf174f328c", "endpoint": "/favicon.ico", "timestamp": "2025-07-01T17:31:56.117290", "total_time_seconds": 0.0010008811950683594, "status_code": 404, "message": "Request completed in 0.0010s with status 404"}
{"event": "request_start", "session_id": "797e739d-0e54-45a9-90b3-ac1ed4f0f814", "request_id": "36dd18a6-4408-4f24-8bdb-03ab04b8ef57", "endpoint": "/docs", "timestamp": "2025-07-01T17:32:00.990664", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "797e739d-0e54-45a9-90b3-ac1ed4f0f814", "request_id": "36dd18a6-4408-4f24-8bdb-03ab04b8ef57", "endpoint": "/docs", "timestamp": "2025-07-01T17:32:00.991664", "total_time_seconds": 0.0010006427764892578, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "797e739d-0e54-45a9-90b3-ac1ed4f0f814", "request_id": "4bc45be3-4769-4621-b20e-69436e440bd0", "endpoint": "/openapi.json", "timestamp": "2025-07-01T17:32:01.164973", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "797e739d-0e54-45a9-90b3-ac1ed4f0f814", "request_id": "4bc45be3-4769-4621-b20e-69436e440bd0", "endpoint": "/openapi.json", "timestamp": "2025-07-01T17:32:01.209556", "total_time_seconds": 0.04458308219909668, "status_code": 200, "message": "Request completed in 0.0446s with status 200"}
{"event": "request_start", "session_id": "797e739d-0e54-45a9-90b3-ac1ed4f0f814", "request_id": "edaeb1be-8c6e-46a5-a825-8c360706b287", "endpoint": "/hybrid_resume", "timestamp": "2025-07-01T17:32:25.906126", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "797e739d-0e54-45a9-90b3-ac1ed4f0f814", "request_id": "edaeb1be-8c6e-46a5-a825-8c360706b287", "endpoint": "/hybrid_resume", "timestamp": "2025-07-01T17:32:25.956263", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "797e739d-0e54-45a9-90b3-ac1ed4f0f814", "request_id": "edaeb1be-8c6e-46a5-a825-8c360706b287", "endpoint": "/hybrid_resume", "timestamp": "2025-07-01T17:32:25.956263", "file_size_bytes": 60820, "message": "Custom metric: file_size_bytes=60820"}
{"event": "custom_metric", "session_id": "797e739d-0e54-45a9-90b3-ac1ed4f0f814", "request_id": "edaeb1be-8c6e-46a5-a825-8c360706b287", "endpoint": "/hybrid_resume", "timestamp": "2025-07-01T17:32:25.957264", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "797e739d-0e54-45a9-90b3-ac1ed4f0f814", "request_id": "edaeb1be-8c6e-46a5-a825-8c360706b287", "endpoint": "/hybrid_resume", "timestamp": "2025-07-01T17:32:25.957264", "extracted_text_length": 2804, "message": "Custom metric: extracted_text_length=2804"}
{"event": "custom_metric", "session_id": "797e739d-0e54-45a9-90b3-ac1ed4f0f814", "request_id": "edaeb1be-8c6e-46a5-a825-8c360706b287", "endpoint": "/hybrid_resume", "timestamp": "2025-07-01T17:32:25.957264", "file_processing_time": 0.04204225540161133, "message": "Custom metric: file_processing_time=0.04204225540161133"}
{"event": "request_complete", "session_id": "797e739d-0e54-45a9-90b3-ac1ed4f0f814", "request_id": "edaeb1be-8c6e-46a5-a825-8c360706b287", "endpoint": "/hybrid_resume", "timestamp": "2025-07-01T17:33:15.571442", "total_time_seconds": 49.66531682014465, "status_code": 200, "message": "Request completed in 49.6653s with status 200"}
{"event": "session_end", "session_id": "797e739d-0e54-45a9-90b3-ac1ed4f0f814", "timestamp": "2025-07-01T17:48:14.895845", "message": "API session ended"}
