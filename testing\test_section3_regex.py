#!/usr/bin/env python3
"""
Test script for the new /section3 regex-based section extraction endpoint.

This script tests the regex pattern matching approach for extracting resume sections.
"""

import os
import sys
import time
import requests
from pathlib import Path

# Configuration
BASE_URL = "http://localhost:8000"

def test_section3_extraction():
    """Test the new regex-based section extraction endpoint."""
    print("🧪 Testing Section3 (Regex) Extraction Capabilities")
    print("=" * 60)
    
    # Find test resume files
    resume_test_files = []
    test_dirs = ["resumes for testing", "resumes", "test_files"]
    
    for test_dir in test_dirs:
        if Path(test_dir).exists():
            resume_test_files.extend(list(Path(test_dir).glob("*.pdf")))
            resume_test_files.extend(list(Path(test_dir).glob("*.docx")))
    
    # Also check for extracted text files to use as test data
    extracted_text_dir = Path("resume_extracted_text")
    if extracted_text_dir.exists():
        text_files = list(extracted_text_dir.glob("*.txt"))
        if text_files:
            print(f"📁 Found {len(text_files)} extracted text files for reference")
            print("   Using actual resume files for testing...")
    
    if not resume_test_files:
        print("❌ No test resume files found!")
        print("Please add some resume files to one of these directories:")
        for test_dir in test_dirs:
            print(f"   - {test_dir}/")
        print("\nAlternatively, you can test with files from these locations:")
        print("   - resume_extracted_text/ (for reference)")
        return
    
    # Test with the first available resume
    test_file = resume_test_files[0]
    print(f"📄 Testing with file: {test_file.name}")
    print(f"📁 File size: {test_file.stat().st_size} bytes")
    print()
    
    # Test the regex method (/section3)
    print("🔍 Testing Regex Pattern Matching Method (/section3)")
    print("-" * 50)
    
    try:
        start_time = time.time()
        
        with open(test_file, 'rb') as f:
            files = {'file': (test_file.name, f, 'application/pdf' if test_file.suffix == '.pdf' else 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')}
            response = requests.post(f"{BASE_URL}/section3", files=files, timeout=300)
        
        processing_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Regex method successful in {processing_time:.2f}s")
            print(f"📊 Overall confidence: {result['overall_confidence']:.2f}")
            print(f"⚡ Processing time: {result['processing_time']:.2f}s")
            print(f"🔧 Extraction method: {result['extraction_method']}")
            print(f"📈 Sections found: {result['extraction_stats']['sections_found']}")
            print(f"🚀 LLM calls made: {result['extraction_stats']['total_calls']}")
            
            # Show section summary
            print("\n📋 Section Extraction Summary:")
            for section, content in result['sections_extracted'].items():
                confidence = result['confidence_scores'].get(section, 0.0)
                status = "✅" if content and content.strip() else "❌"
                content_preview = content[:50] + "..." if len(content) > 50 else content
                print(f"   {status} {section.capitalize()}: {confidence:.2f} - {content_preview}")
                
            # Show detailed extraction stats
            print(f"\n📊 Detailed Stats:")
            stats = result['extraction_stats']
            for key, value in stats.items():
                print(f"   {key}: {value}")
                
        else:
            print(f"❌ Regex method failed: {response.status_code}")
            print(f"Error: {response.text}")
    
    except Exception as e:
        print(f"❌ Regex method error: {e}")
    
    print("\n" + "=" * 60)

def compare_with_other_methods():
    """Compare regex method with LLM-based methods."""
    print("🔄 Comparing Section Extraction Methods")
    print("=" * 60)
    
    # Find test resume files
    resume_test_files = []
    test_dirs = ["resumes for testing", "resumes", "test_files"]
    
    for test_dir in test_dirs:
        if Path(test_dir).exists():
            resume_test_files.extend(list(Path(test_dir).glob("*.pdf")))
            resume_test_files.extend(list(Path(test_dir).glob("*.docx")))
    
    if not resume_test_files:
        print("❌ No test resume files found for comparison!")
        return
    
    test_file = resume_test_files[0]
    print(f"📄 Comparing methods with: {test_file.name}")
    print()
    
    methods = [
        ("/section", "Multiple LLM Calls"),
        ("/section2", "Single LLM Call"),
        ("/section3", "Regex Pattern Matching")
    ]
    
    results = {}
    
    for endpoint, method_name in methods:
        print(f"Testing {method_name} ({endpoint})...")
        try:
            start_time = time.time()
            
            with open(test_file, 'rb') as f:
                files = {'file': (test_file.name, f, 'application/pdf' if test_file.suffix == '.pdf' else 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')}
                response = requests.post(f"{BASE_URL}{endpoint}", files=files, timeout=300)
            
            processing_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                results[method_name] = {
                    'success': True,
                    'processing_time': result['processing_time'],
                    'overall_confidence': result['overall_confidence'],
                    'sections_found': result['extraction_stats']['sections_found'],
                    'llm_calls': result['extraction_stats']['total_calls'],
                    'method': result['extraction_method']
                }
                print(f"   ✅ Success in {processing_time:.2f}s")
            else:
                results[method_name] = {
                    'success': False,
                    'error': f"HTTP {response.status_code}"
                }
                print(f"   ❌ Failed: HTTP {response.status_code}")
                
        except Exception as e:
            results[method_name] = {
                'success': False,
                'error': str(e)
            }
            print(f"   ❌ Error: {e}")
    
    # Display comparison
    print("\n📊 Method Comparison Results:")
    print("-" * 50)
    
    for method_name, result in results.items():
        if result['success']:
            print(f"\n🔧 {method_name}:")
            print(f"   ⏱️  Processing Time: {result['processing_time']:.2f}s")
            print(f"   📊 Confidence: {result['overall_confidence']:.2f}")
            print(f"   📋 Sections Found: {result['sections_found']}")
            print(f"   🤖 LLM Calls: {result['llm_calls']}")
            print(f"   🏷️  Method: {result['method']}")
        else:
            print(f"\n❌ {method_name}: {result['error']}")
    
    print("\n🎯 Key Observations:")
    print("   - Regex method should be fastest (0 LLM calls)")
    print("   - Multiple calls method should be most accurate")
    print("   - Single call method should be balanced")
    print("   - Regex method works best with well-formatted resumes")

def check_extracted_files():
    """Check the extracted section files."""
    print("\n📁 Checking Extracted Section Files")
    print("-" * 50)
    
    sections_dir = Path("resume sections extracted")
    if sections_dir.exists():
        section_files = list(sections_dir.glob("*.txt"))
        print(f"📁 Found {len(section_files)} section extraction files")
        
        if section_files:
            # Show recent files
            recent_files = sorted(section_files, key=lambda x: x.stat().st_mtime, reverse=True)[:3]
            print("\n📄 Recent extraction files:")
            for file in recent_files:
                size = file.stat().st_size
                mtime = time.ctime(file.stat().st_mtime)
                print(f"   - {file.name} ({size} bytes, {mtime})")
                
                # Check if it's a regex extraction
                if "regex" in file.name:
                    print(f"     🔍 This is a regex extraction file!")
        else:
            print("📭 No section extraction files found")
    else:
        print("📭 No 'resume sections extracted' directory found")

def main():
    """Main test function."""
    print("🚀 Section3 Regex Extraction Test Suite")
    print("Testing the new regex-based section extraction endpoint")
    print()
    
    # Test 1: Basic regex extraction
    test_section3_extraction()
    
    # Test 2: Compare with other methods
    compare_with_other_methods()
    
    # Test 3: Check extracted files
    check_extracted_files()
    
    print("\n" + "=" * 60)
    print("🎉 Testing completed!")
    print("\n📝 Summary:")
    print("✅ Tested regex-based section extraction (/section3)")
    print("✅ Compared with LLM-based methods")
    print("✅ Checked extracted files")
    
    print("\n🎯 Expected Benefits of Regex Method:")
    print("   - Fastest processing (no LLM calls)")
    print("   - Consistent results")
    print("   - Good for well-formatted resumes")
    print("   - Lower resource usage")
    
    print("\n⚠️  Limitations:")
    print("   - Less flexible with varied formatting")
    print("   - May miss sections with unusual headers")
    print("   - Requires clear section boundaries")

if __name__ == "__main__":
    main()
