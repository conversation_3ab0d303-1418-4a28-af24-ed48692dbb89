{"timestamp": "20250701_154810_991", "calculation_method": "CGPA", "total_credits_used": 10.0, "processing_time_seconds": 0.0009987354278564453, "final_score": 8.75, "fit_category": "Excellent Match", "summary": "The candidate is a excellent match for this position with a CGPA-style score of 8.8/10. Key strengths: Skills, Experience, Education, Location. Areas for improvement: Certifications.", "field_scores": {"skills": {"raw_score": 9.0, "normalized_score": 9.0, "weight": 3.0, "weighted_score": 27.0, "rationale": "Matched 8/8 required skills and 2/4 preferred skills. Matched: Python, JavaScript, React, Node.js, SQL, REST APIs, Git, AWS", "details": {"resume_skills_count": 10, "required_skills_count": 8, "preferred_skills_count": 4, "matched_required": ["Python", "JavaScript", "React", "Node.js", "SQL", "REST APIs", "Git", "AWS"], "missing_required": [], "matched_preferred": ["<PERSON>er", "Machine Learning"], "required_match_ratio": 1.0, "preferred_match_ratio": 0.5}}, "experience": {"raw_score": 10.0, "normalized_score": 10.0, "weight": 2.5, "weighted_score": 25.0, "rationale": "Excellent match: 4 years vs required 3 years", "details": {"candidate_yoe": 4, "required_yoe": 3, "experience_ratio": 1.3333333333333333, "experience_entries_count": 2}}, "education": {"raw_score": 10.0, "normalized_score": 10.0, "weight": 2.0, "weighted_score": 20.0, "rationale": "Education requirements met: 'B.Tech Computer Science' matches requirement 'Bachelor's degree in Computer Science or related field' with academic performance of 95.0%", "details": {"education_entries_count": 1, "education_requirements_count": 1, "education_match": true, "matched_degree": "B.Tech Computer Science", "matched_requirement": "Bachelor's degree in Computer Science or related field", "candidate_degrees": ["B.Tech Computer Science"], "max_gpa_percentage": 95.0}}, "certifications": {"raw_score": 2.0, "normalized_score": 2.0, "weight": 1.0, "weighted_score": 2.0, "rationale": "Found 1 relevant certifications: AWS Certified Developer", "details": {"total_certifications": 1, "relevant_certifications": ["AWS Certified Developer"], "relevant_count": 1, "all_certifications": ["AWS Certified Developer"]}}, "location": {"raw_score": 10.0, "normalized_score": 10.0, "weight": 1.0, "weighted_score": 10.0, "rationale": "Current location (san francisco, ca) matches job location (san francisco, ca)", "details": {"jd_location": "san francisco, ca", "resume_location": "san francisco, ca", "experience_locations": ["san francisco, ca", "san francisco, ca"], "has_location_info": true}}, "reliability": {"raw_score": 7.0, "normalized_score": 7.0, "weight": 0.5, "weighted_score": 3.5, "rationale": "Good stability: average 2.0 years per company", "details": {"candidate_yoe": 4, "num_companies": 2, "avg_tenure": 2.0, "has_experience_data": true}}}}