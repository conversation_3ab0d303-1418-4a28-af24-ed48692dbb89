{"name": "<PERSON>", "email": "<EMAIL>", "phone": "******-0123", "location": "San Francisco, CA", "skills": ["Python", "JavaScript", "React", "Node.js", "SQL", "MongoDB", "REST APIs", "Git", "AWS", "<PERSON>er", "HTML", "CSS"], "experience": [{"company": "Tech Corp", "position": "Senior Software Engineer", "duration": "2020 - Present", "location": "San Francisco, CA", "description": "Led development of web applications using React and Node.js"}, {"company": "StartupXYZ", "position": "Full Stack Developer", "duration": "2018 - 2020", "location": "San Francisco, CA", "description": "Developed full-stack applications using Python and JavaScript"}, {"company": "WebDev Inc", "position": "Junior Developer", "duration": "2016 - 2018", "location": "San Jose, CA", "description": "Built web applications and learned modern development practices"}], "education": [{"degree": "Bachelor of Science in Computer Science", "institution": "University of California, Berkeley", "year": "2016", "gpa": "3.7"}], "certifications": [{"name": "AWS Certified Developer", "issuer": "Amazon Web Services", "year": "2021"}, {"name": "React Developer Certification", "issuer": "Meta", "year": "2020"}]}