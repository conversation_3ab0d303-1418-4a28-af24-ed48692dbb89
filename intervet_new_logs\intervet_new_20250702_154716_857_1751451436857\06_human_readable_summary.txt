
INTERVET_NEW CALCULATION SUMMARY
================================
Timestamp: 20250702_154716_857
Processing Time: 0.006 seconds

FINAL RESULT
============
Total Score: 4.62/10
Fit Category: Moderate Match
Summary: The candidate is a moderate match for this position with a CGPA-style score of 4.6/10. Key strengths: Education. Areas for improvement: Skills, Certifications.

WEIGHTAGE CONFIGURATION
=======================
Skills: 4.0
Experience: 3.0
Education: 2.0
Certifications: 0.5
Location: 0.5
Reliability: 0.0
Total Credits: 10.0

DETAILED FIELD SCORES
=====================
Skills:
  Raw Score: 2.18/10
  Weight: 4.0
  Weighted Score: 8.73
  Rationale: Matched 3/11 required skills and 0/7 preferred skills. Matched: Python, PostgreSQL, PyTorch. Missing: NLP, Generative AI, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Transformers

Experience:
  Raw Score: 5.00/10
  Weight: 3.0
  Weighted Score: 15.00
  Rationale: Could not determine candidate's experience to compare with required 2 years

Education:
  Raw Score: 10.00/10
  Weight: 2.0
  Weighted Score: 20.00
  Rationale: Education requirements fully met: 'B.Tech Computer Science Engineering (Artificial Intelligence & Machine Learning)' matches requirement 'Bachelor's degree in Computer Science, Engineering, Computational Linguistics, Data Science or a related quantitative field' (Match type: exact_match)

Certifications:
  Raw Score: 0.00/10
  Weight: 0.5
  Weighted Score: 0.00
  Rationale: No certifications found in resume

Location:
  Raw Score: 5.00/10
  Weight: 0.5
  Weighted Score: 2.50
  Rationale: Location information not available for comparison

Reliability:
  Raw Score: 5.00/10
  Weight: 0.0
  Weighted Score: 0.00
  Rationale: Could not calculate job stability due to missing experience data

CALCULATION FORMULA
===================
Final Score = (Sum of Weighted Scores) / (Sum of Weights)
Final Score = (8.73 + 15.00 + 20.00 + 0.00 + 2.50 + 0.00) / 10.0
Final Score = 4.62/10
