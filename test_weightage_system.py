#!/usr/bin/env python3
"""
Test script to verify the new weightage system for the intervet endpoint.
Tests different weightage configurations and validates the scoring.
"""

import requests
import json
import os
import time

# Configuration
API_BASE_URL = "http://localhost:8000"
TEST_RESUME_PATH = "resumes for testing/Resume-Raman <PERSON>.pdf"

def get_sample_data():
    """Get sample resume and JD data for testing."""
    
    # Parse a sample resume
    print("📄 Parsing sample resume...")
    with open(TEST_RESUME_PATH, 'rb') as resume_file:
        files = {'file': (os.path.basename(TEST_RESUME_PATH), resume_file, 'application/pdf')}
        response = requests.post(f"{API_BASE_URL}/hybrid_resume", files=files, timeout=120)
        
        if response.status_code != 200:
            raise Exception(f"Resume parsing failed: {response.text}")
        
        resume_data = response.json()
        print("✅ Resume parsed successfully")
    
    # Sample JD data
    jd_data = {
        "job_title": "Senior Software Engineer",
        "company_name": "TechCorp Solutions",
        "location": "Bangalore, India",
        "required_skills": [
            "Python", "JavaScript", "Java", "React", "Node.js", 
            "AWS", "SQL", "NoSQL", "Microservices", "CI/CD"
        ],
        "preferred_skills": [
            "Machine Learning", "Docker", "Kubernetes", "Data Science"
        ],
        "required_experience": "5+ years of experience in software development",
        "education_requirements": ["Bachelor's degree in Computer Science or related field"],
        "education_details": {
            "degree_level": "Bachelor's",
            "field_of_study": "Computer Science",
            "alternatives": "related field"
        }
    }
    
    return resume_data, jd_data

def test_weightage_configuration(resume_data, jd_data, weightage_config, test_name):
    """Test a specific weightage configuration."""
    
    print(f"\n🧪 Testing: {test_name}")
    print(f"📊 Weightage: {weightage_config}")
    
    payload = {
        "resume_json": resume_data,
        "jd_json": jd_data,
        "weightage": weightage_config
    }
    
    start_time = time.time()
    response = requests.post(
        f"{API_BASE_URL}/intervet", 
        json=payload, 
        timeout=120,
        headers={"Content-Type": "application/json"}
    )
    end_time = time.time()
    
    print(f"⏱️  Request completed in {end_time - start_time:.2f} seconds")
    print(f"📊 Response status: {response.status_code}")
    
    if response.status_code != 200:
        print(f"❌ Request failed: {response.text}")
        return None
    
    result = response.json()
    total_score = result.get("total_score", 0)
    fit_category = result.get("fit_category", "Unknown")
    detailed_scores = result.get("detailed_scores", {})
    
    print(f"🎯 Total Score: {total_score}/100")
    print(f"📈 Fit Category: {fit_category}")
    
    # Calculate expected percentages
    total_weight = sum(weightage_config.values())
    if total_weight == 0:
        # For all-zero case, system should use equal distribution
        num_categories = len([k for k in weightage_config.keys() if k != 'alma_mater'])  # Exclude alma_mater from equal distribution
        expected_percentages = {k: 100/num_categories if k != 'alma_mater' else 0 for k in weightage_config.keys()}
    else:
        expected_percentages = {k: (v / total_weight) * 100 for k, v in weightage_config.items()}
    
    print(f"📋 Score Breakdown:")
    for category, score in detailed_scores.items():
        expected_max = expected_percentages.get(category, 0)
        percentage = (score / expected_max) * 100 if expected_max > 0 else 0
        print(f"   • {category.replace('_', ' ').title()}: {score:.2f}/{expected_max:.2f} ({percentage:.1f}%)")
    
    # Verify total score equals sum of detailed scores
    calculated_total = sum(detailed_scores.values())
    print(f"📊 Score verification: {calculated_total:.2f} (calculated) vs {total_score:.2f} (returned)")
    
    if abs(calculated_total - total_score) > 0.1:
        print("❌ Score mismatch detected!")
        return False
    else:
        print("✅ Score calculation verified")
    
    return True

def test_weightage_system():
    """Test the weightage system with different configurations."""
    
    print("🧪 Testing Weightage System")
    print("=" * 50)
    
    # Get sample data
    try:
        resume_data, jd_data = get_sample_data()
    except Exception as e:
        print(f"❌ Failed to get sample data: {e}")
        return False
    
    # Test configurations
    test_configs = [
        {
            "name": "Equal Weightage (Default)",
            "weightage": {
                "skills_match_direct": 5,
                "experience_match": 5,
                "reliability": 5,
                "certifications": 5,
                "location_match": 5,
                "academic_match": 5,
                "alma_mater": 0
            }
        },
        {
            "name": "Skills & Experience Focus",
            "weightage": {
                "skills_match_direct": 5,
                "experience_match": 5,
                "reliability": 3,
                "certifications": 2,
                "location_match": 1,
                "academic_match": 2,
                "alma_mater": 0
            }
        },
        {
            "name": "Skills Heavy",
            "weightage": {
                "skills_match_direct": 5,
                "experience_match": 3,
                "reliability": 2,
                "certifications": 1,
                "location_match": 1,
                "academic_match": 1,
                "alma_mater": 0
            }
        },
        {
            "name": "Academic & Alma Mater Focus",
            "weightage": {
                "skills_match_direct": 3,
                "experience_match": 3,
                "reliability": 2,
                "certifications": 1,
                "location_match": 1,
                "academic_match": 4,
                "alma_mater": 3
            }
        },
        {
            "name": "Location Priority",
            "weightage": {
                "skills_match_direct": 3,
                "experience_match": 3,
                "reliability": 2,
                "certifications": 1,
                "location_match": 5,
                "academic_match": 2,
                "alma_mater": 0
            }
        }
    ]
    
    results = []
    
    for config in test_configs:
        success = test_weightage_configuration(
            resume_data, 
            jd_data, 
            config["weightage"], 
            config["name"]
        )
        results.append(success)
    
    # Test edge cases
    print(f"\n🧪 Testing Edge Cases")
    
    # Test with all zeros (should use equal distribution)
    print(f"\n🔍 Testing all-zero weightage...")
    zero_config = {k: 0 for k in test_configs[0]["weightage"].keys()}
    zero_success = test_weightage_configuration(
        resume_data, 
        jd_data, 
        zero_config, 
        "All Zero Weightage"
    )
    results.append(zero_success)
    
    # Test without weightage parameter (should use default)
    print(f"\n🔍 Testing without weightage parameter...")
    payload_no_weightage = {
        "resume_json": resume_data,
        "jd_json": jd_data
    }
    
    response = requests.post(
        f"{API_BASE_URL}/intervet", 
        json=payload_no_weightage, 
        timeout=120,
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ No weightage parameter: Score = {result.get('total_score', 0)}/100")
        results.append(True)
    else:
        print(f"❌ No weightage parameter test failed: {response.text}")
        results.append(False)
    
    return all(results)

def main():
    """Main test function."""
    
    print("🧪 Weightage System Test Suite")
    print("=" * 50)
    print("Testing:")
    print("1. ✅ Custom weightage configurations")
    print("2. ✅ Score calculation accuracy")
    print("3. ✅ Percentage distribution")
    print("4. ✅ Edge cases (all zero, no weightage)")
    print("5. ✅ Different priority scenarios")
    print("=" * 50)
    
    # Check API health
    try:
        response = requests.get(f"{API_BASE_URL}/", timeout=10)
        if response.status_code != 200:
            print("❌ API health check failed")
            return
        print("✅ API is running and healthy")
    except Exception as e:
        print(f"❌ Could not reach API: {e}")
        print("\n💡 Make sure to start the API server first:")
        print("   python main.py")
        return
    
    # Test weightage system
    success = test_weightage_system()
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"✅ Weightage system: {'PASSED' if success else 'FAILED'}")
    
    if success:
        print("\n🎉 All tests passed! Weightage system is working correctly.")
        print("\n📋 Features Verified:")
        print("   ✅ Custom weightage configuration (0-5 scale)")
        print("   ✅ Dynamic percentage calculation")
        print("   ✅ Score distribution based on weightage")
        print("   ✅ Total score always sums to 100")
        print("   ✅ Default weightage when not specified")
        print("   ✅ Edge case handling (all zero weights)")
        print("   ✅ Academic match included (5% default)")
        print("   ✅ Alma mater available (0% default)")
    else:
        print("\n❌ Some tests failed. Check the error messages above.")

if __name__ == "__main__":
    main()
