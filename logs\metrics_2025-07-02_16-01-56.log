{"event": "session_start", "session_id": "43012055-90ef-4f9c-b78f-4c699f116793", "timestamp": "2025-07-02T16:01:56.845867", "message": "New API session started"}
{"event": "request_start", "session_id": "43012055-90ef-4f9c-b78f-4c699f116793", "request_id": "c06d87ad-e6f6-437f-8b04-1df35b11ebed", "endpoint": "/", "timestamp": "2025-07-02T16:01:58.157188", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "43012055-90ef-4f9c-b78f-4c699f116793", "request_id": "c06d87ad-e6f6-437f-8b04-1df35b11ebed", "endpoint": "/", "timestamp": "2025-07-02T16:01:58.160189", "total_time_seconds": 0.0030014514923095703, "status_code": 200, "message": "Request completed in 0.0030s with status 200"}
{"event": "request_start", "session_id": "43012055-90ef-4f9c-b78f-4c699f116793", "request_id": "faf292a0-ab06-49a5-bd3d-c881378f8ac2", "endpoint": "/docs", "timestamp": "2025-07-02T16:02:00.268558", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "43012055-90ef-4f9c-b78f-4c699f116793", "request_id": "faf292a0-ab06-49a5-bd3d-c881378f8ac2", "endpoint": "/docs", "timestamp": "2025-07-02T16:02:00.269558", "total_time_seconds": 0.0009999275207519531, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "43012055-90ef-4f9c-b78f-4c699f116793", "request_id": "86736724-5b39-467d-a426-efc25c52690f", "endpoint": "/openapi.json", "timestamp": "2025-07-02T16:02:00.329586", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "43012055-90ef-4f9c-b78f-4c699f116793", "request_id": "86736724-5b39-467d-a426-efc25c52690f", "endpoint": "/openapi.json", "timestamp": "2025-07-02T16:02:00.345583", "total_time_seconds": 0.015996456146240234, "status_code": 200, "message": "Request completed in 0.0160s with status 200"}
{"event": "request_start", "session_id": "43012055-90ef-4f9c-b78f-4c699f116793", "request_id": "81766eed-6557-453e-9936-70ca4eb4c8c4", "endpoint": "/intervet_new", "timestamp": "2025-07-02T16:02:20.311374", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "43012055-90ef-4f9c-b78f-4c699f116793", "request_id": "81766eed-6557-453e-9936-70ca4eb4c8c4", "endpoint": "/intervet_new", "timestamp": "2025-07-02T16:02:20.312372", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "43012055-90ef-4f9c-b78f-4c699f116793", "request_id": "81766eed-6557-453e-9936-70ca4eb4c8c4", "endpoint": "/intervet_new", "timestamp": "2025-07-02T16:02:20.327370", "final_score": 5.839160839160839, "message": "Custom metric: final_score=5.839160839160839"}
{"event": "custom_metric", "session_id": "43012055-90ef-4f9c-b78f-4c699f116793", "request_id": "81766eed-6557-453e-9936-70ca4eb4c8c4", "endpoint": "/intervet_new", "timestamp": "2025-07-02T16:02:20.327370", "fit_category": "Good Match", "message": "Custom metric: fit_category=Good Match"}
{"event": "custom_metric", "session_id": "43012055-90ef-4f9c-b78f-4c699f116793", "request_id": "81766eed-6557-453e-9936-70ca4eb4c8c4", "endpoint": "/intervet_new", "timestamp": "2025-07-02T16:02:20.327370", "total_credits_used": 13.0, "message": "Custom metric: total_credits_used=13.0"}
{"event": "custom_metric", "session_id": "43012055-90ef-4f9c-b78f-4c699f116793", "request_id": "81766eed-6557-453e-9936-70ca4eb4c8c4", "endpoint": "/intervet_new", "timestamp": "2025-07-02T16:02:20.327370", "log_folder": "intervet_new_logs\\intervet_new_20250702_160220_321_1751452340321", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250702_160220_321_1751452340321"}
{"event": "request_complete", "session_id": "43012055-90ef-4f9c-b78f-4c699f116793", "request_id": "81766eed-6557-453e-9936-70ca4eb4c8c4", "endpoint": "/intervet_new", "timestamp": "2025-07-02T16:02:20.328373", "total_time_seconds": 0.016998767852783203, "status_code": 200, "message": "Request completed in 0.0170s with status 200"}
{"event": "session_end", "session_id": "43012055-90ef-4f9c-b78f-4c699f116793", "timestamp": "2025-07-02T16:02:46.455577", "message": "API session ended"}
