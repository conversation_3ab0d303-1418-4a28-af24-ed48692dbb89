#!/usr/bin/env python3
"""
Test the two problematic resumes that were failing.
"""

import requests
import json
import time
from pathlib import Path

BASE_URL = "http://localhost:8000"

def test_problematic_resume(file_path):
    """Test a specific problematic resume."""
    
    print(f"\n🧪 Testing Problematic Resume: {file_path.name}")
    print("=" * 60)
    
    try:
        # Check file exists
        if not file_path.exists():
            print(f"❌ File not found: {file_path}")
            return False
        
        file_size = file_path.stat().st_size
        print(f"📄 File size: {file_size:,} bytes")
        
        # Determine content type
        if file_path.suffix.lower() == '.pdf':
            content_type = 'application/pdf'
        elif file_path.suffix.lower() == '.docx':
            content_type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        else:
            content_type = 'application/octet-stream'
        
        print(f"📋 Content type: {content_type}")
        
        # Test the hybrid endpoint
        start_time = time.time()
        
        with open(file_path, 'rb') as f:
            files = {'file': (file_path.name, f, content_type)}
            response = requests.post(f"{BASE_URL}/hybrid_resume", files=files, timeout=180)
        
        processing_time = time.time() - start_time
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"⏱️ Processing Time: {processing_time:.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ SUCCESS!")
            
            # Check if there's an error in the response
            if result.get('error'):
                print(f"⚠️ Response contains error: {result['error']}")
                if result.get('error_details'):
                    print(f"   Details: {result['error_details']}")
            
            # Analyze the response
            print(f"\n📊 Response Analysis:")
            print(f"   👤 Name: {result.get('name', 'Not found')}")
            print(f"   📧 Email: {result.get('email', 'Not found')}")
            print(f"   📞 Phone: {result.get('phone', 'Not found')}")
            print(f"   🎓 Education: {len(result.get('education', []))} entries")
            print(f"   💼 Experience: {len(result.get('experience', []))} entries")
            print(f"   🛠️ Skills: {len(result.get('skills', []))} items")
            print(f"   🚀 Projects: {len(result.get('projects', []))} items")
            print(f"   🏆 Certifications: {len(result.get('certifications', []))} items")
            
            # Check processing metadata
            print(f"\n🔧 Processing Metadata:")
            print(f"   ⏱️ Processing time: {result.get('processing_time', 'N/A')}")
            print(f"   🔧 Method: {result.get('extraction_method', 'N/A')}")
            print(f"   📋 Sections extracted: {result.get('sections_extracted', 'N/A')}")
            print(f"   📈 Regex confidence: {result.get('regex_confidence', 'N/A')}")
            
            # Show sample data if available
            if result.get('skills') and len(result['skills']) > 0:
                skills = result['skills']
                if isinstance(skills, dict):
                    skill_names = list(skills.keys())[:3]
                    print(f"   🛠️ Sample skills: {skill_names}")
                elif isinstance(skills, list):
                    print(f"   🛠️ Sample skills: {skills[:3]}")
            
            if result.get('education') and len(result['education']) > 0:
                edu = result['education'][0]
                print(f"   🎓 Sample education: {edu.get('degree', 'N/A')} at {edu.get('institution', 'N/A')}")
            
            return True
            
        else:
            print("❌ FAILED!")
            print(f"Error: {response.text}")
            
            # Try to parse error details
            try:
                error_data = response.json()
                print(f"Error detail: {error_data.get('detail', 'No detail provided')}")
            except:
                pass
            
            return False
            
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Test the problematic resumes."""
    
    print("🐛 Testing Previously Problematic Resumes")
    print("=" * 70)
    
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print("❌ Server not responding correctly")
            return
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        print("Start server with: python main.py")
        return
    
    # Test the two problematic resumes
    problematic_resumes = [
        Path("resumes for testing/Emily Yan.pdf"),
        Path("resumes for testing/Resume-Raman Luhach.pdf")
    ]
    
    results = {}
    
    for resume_path in problematic_resumes:
        success = test_problematic_resume(resume_path)
        results[resume_path.name] = success
        
        # Add delay between tests
        time.sleep(2)
    
    # Summary
    print(f"\n{'='*70}")
    print("📊 TESTING SUMMARY")
    print(f"{'='*70}")
    
    successful = [name for name, success in results.items() if success]
    failed = [name for name, success in results.items() if not success]
    
    print(f"✅ Successful: {len(successful)}/{len(problematic_resumes)}")
    for name in successful:
        print(f"   ✅ {name}")
    
    if failed:
        print(f"\n❌ Still failing: {len(failed)}/{len(problematic_resumes)}")
        for name in failed:
            print(f"   ❌ {name}")
    
    if len(successful) == len(problematic_resumes):
        print(f"\n🎉 All previously problematic resumes now working!")
        print("✅ Error handling improvements successful")
    else:
        print(f"\n⚠️ Some resumes still have issues")
        print("Check the error messages above for details")
    
    print(f"\n🎯 Next Steps:")
    if failed:
        print("1. Check conversation logs for detailed error information")
        print("2. Look at the specific error messages in the responses")
        print("3. Consider additional error handling improvements")
    else:
        print("✅ All resumes working - ready for production!")
        print("✅ Error handling is robust")
        print("✅ Hybrid endpoint is stable")

if __name__ == "__main__":
    main()
