{
    "title": "Java/J2EE Developer",
    "company": "FM Global",
    "location": "Alpharetta, GA",
    "dates": "Apr 2013 – Nov 2015",
    "responsibilities": [
      "Engineered microservices architecture using J2EE, ensuring modular scalability and efficient resource utilization.",
      "Developed RESTful APIs with Spring Boot and deployed on JBoss and WebLogic servers, optimizing backend performance and deployment efficiency.",
      "Integrated Oracle databases using Hibernate ORM, ensuring seamless data persistence and efficient data retrieval.",
      "Designed and implemented frontend interfaces with Apache Struts MVC, utilizing AJAX for dynamic content retrieval and improved user interaction.",
      "Implemented CI/CD pipelines with Jenkins for automated builds and deployments, ensuring continuous integration and delivery of software updates.",
      "Optimized SQL queries and database interactions for MySQL and PostgreSQL, Cassandra performance by configuring replication strategies, compaction settings, repair operations to minimize downtime and latency.",
      "Implemented unit tests using JUnit, Mockito and Selenium for backend services and UI components, ensuring robust code quality and functionality."
    ],
    "technologies": [
      "J2EE",
      "Spring Boot",
      "JBoss",
      "WebLogic",
      "Oracle",
      "MySQL",
      "PostgreSQL",
      "Cassandra",
      "JUnit",
      "Mockito",
      "Selenium"
    ]
  },
  {
    "title": "Java Developer",
    "company": "FM Global",
    "location": "Alpharetta, GA",
    "dates": "Apr 2013 – Nov 2015",
    "responsibilities": [
      "Developed microservices architecture using J2EE, ensuring modular scalability and efficient resource utilization.",
      "Developed RESTful APIs with Spring Boot and deployed on JBoss and WebLogic servers, optimizing backend performance and deployment efficiency.",
      "Integrated Oracle databases using Hibernate ORM, ensuring seamless data persistence and efficient data retrieval.",
      "Designed and implemented frontend interfaces with Apache Struts MVC, utilizing AJAX for dynamic content retrieval and improved user interaction.",
      "Implemented CI/CD pipelines with Jenkins for automated builds and deployments, ensuring continuous integration and delivery of software updates.",
      "Optimized SQL queries and database interactions for MySQL and PostgreSQL, Cassandra performance by configuring replication strategies, compaction settings, repair operations to minimize downtime and latency.",
      "Implemented unit tests using JUnit, Mockito and Selenium for backend services and UI components, ensuring robust code quality and functionality."
    ],
    "technologies": [
      "J2EE",
      "Spring Boot",
      "JBoss",
      "WebLogic",
      "Oracle",
      "MySQL",
      "PostgreSQL",
      "Cassandra",
      "JUnit",
      "Mockito",
      "Selenium"
    ]
  },
  {
    "title": "Java/J2EE Developer",
    "company": "Target",
    "location": "Minneapolis, MN",
    "dates": "Dec 2015 – Feb 2018",
    "responsibilities": [
      "Engineered microservices architecture using J2EE, ensuring modular scalability and efficient resource utilization.",
      "Developed RESTful APIs with Spring Boot and deployed on JBoss and WebLogic servers, optimizing backend performance and deployment efficiency.",
      "Integrated Oracle databases using Hibernate ORM, ensuring seamless data persistence and efficient data retrieval.",
      "Designed and implemented frontend interfaces with Apache Struts MVC, utilizing AJAX for dynamic content retrieval and improved user interaction.",
      "Implemented CI/CD pipelines with Jenkins for automated builds and deployments, ensuring continuous integration and delivery of software updates.",
      "Optimized SQL queries and database interactions for MySQL and PostgreSQL, Cassandra performance by configuring replication strategies, compaction settings, repair operations to minimize downtime and latency.",
      "Implemented unit tests using JUnit, Mockito and Selenium for backend services and UI components, ensuring robust code quality and functionality."
    ],
    "technologies": [
      "J2EE",
      "Spring Boot",
      "JBoss",
      "WebLogic",
      "Oracle",
      "MySQL",
      "PostgreSQL",
      "Cassandra",
      "JUnit",
      "Mockito",
      "Selenium"
    ]
  }