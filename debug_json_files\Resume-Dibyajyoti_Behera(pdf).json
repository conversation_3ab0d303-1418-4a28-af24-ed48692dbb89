{
    "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    "email": null,
    "phone": null,
    "education": [
        {
            "degree": "Bachelor of Technology (AI ML)",
            "institution": "Newton School of Technology, Rishihood University",
            "year": "2023 - 2027"
        },
        {
            "degree": "Intermediate (Class XII)",
            "institution": "St Xavier's High School",
            "year": "2020 - 2022"
        },
        {
            "degree": "Matriculation (Class X)",
            "institution": "St Xavier's High School",
            "year": "2017 - 2020"
        }
    ],
    "skills": [
        "Java",
        "Python",
        "JavaScript",
        "CSS",
        "HTML",
        "React",
        "Express JS",
        "Prisma ORM",
        "MySQL",
        "Tailwind",
        "NodeJS",
        "Data Structure"
    ],
    "experience": [],
    "projects": [
        {
            "name": "Voting App",
            "description": "Developed a backend voting system with RESTful APIs for candidate management, user authentication and voting, integrating JWT for secure access and ensuring ecient database interactions. Implemented secure user authentication with login and registration functionalities. Developed CRUD operations for managing candidates, ensuring ecient data handling. Built robust API endpoints for secure vote submission and retrieval, ensuring seamless functionality with data validation."
        },
        {
            "name": "Algo Visualiser",
            "description": "Built an interactive platform to visualize sorting algorithms like bubble sort, selection sort, and insertion sort, allowing real-time user interaction and understanding of algorithm behavior. Enabled custom input array creation to test various data sets, enhancing algorithm testing exibility. Implemented pause and resume controls for user-driven visualization, improving interaction and learning. Incorporated dynamic color changes and animation to clearly indicate the stages of the sorting process. Developed adjustable algorithm speed to provide a customized learning experience based on user preference."
        },
        {
            "name": "Soundly",
            "description": "Developed a dynamic web-based music streaming application, enabling users to access a vast library of songs and albums, and providing an intuitive and seamless experience for discovering, searching, and listening to music. Integrated external APIs to fetch and display music content, providing seamless browsing, searching, and streaming experiences, while enhancing content discovery through a diverse catalog of songs and albums. Implemented a secure user authentication system to streamline registration and login processes, enhancing data protection and user access control."
        }
    ],
    "certifications": [],
    "domain_of_interest": [
        "Web Development",
        "Data Structures and Algorithms"
    ],
    "languages_known": [
        "Java",
        "Python",
        "JavaScript",
        "CSS",
        "HTML"
    ],
    "achievements": [
        "Achieved Top 5 Rank in the District-Level Inter-School Story Writing Competition"
    ],
    "publications": [],
    "volunteer_experience": [],
    "references": [],
    "summary": "Web developer with a strong foundation in full-stack technologies (HTML, CSS, JavaScript, React, Node.js, Express, MySQL). Skilled in building dynamic, responsive, user-focused interfaces and creating backend APIs, with a passion for solving problems. Focused on improving technical skills, particularly in Data Structures and Algorithms (DSA), and eager to expand full-stack development capabilities.",
    "personal_projects": [],
    "social_media": [
        "null",
        "null"
    ]
}