"""
Test script for section extraction endpoints.

This script tests both /section and /section2 endpoints to evaluate
the Gemma model's capabilities for resume section extraction.
"""

import requests
import json
import os
import time
from pathlib import Path

# API base URL
BASE_URL = "http://localhost:8000"

def test_section_extraction():
    """Test both section extraction endpoints."""
    print("🧪 Testing Section Extraction Capabilities")
    print("=" * 60)
    
    # Find test resume files
    resume_test_files = []
    test_dirs = ["resumes for testing", "resumes", "test_files"]
    
    for test_dir in test_dirs:
        if Path(test_dir).exists():
            resume_test_files.extend(list(Path(test_dir).glob("*.pdf")))
            resume_test_files.extend(list(Path(test_dir).glob("*.docx")))
    
    if not resume_test_files:
        print("❌ No test resume files found!")
        print("Please add some resume files to one of these directories:")
        for test_dir in test_dirs:
            print(f"   - {test_dir}/")
        return
    
    # Test with the first available resume
    test_file = resume_test_files[0]
    print(f"📄 Testing with file: {test_file.name}")
    print(f"📁 File size: {test_file.stat().st_size} bytes")
    print()
    
    # Test 1: Multiple calls method (/section)
    print("1. Testing Multiple Calls Method (/section)")
    print("-" * 50)
    
    try:
        start_time = time.time()
        
        with open(test_file, 'rb') as f:
            files = {'file': (test_file.name, f, 'application/pdf' if test_file.suffix == '.pdf' else 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')}
            response = requests.post(f"{BASE_URL}/section", files=files, timeout=300)
        
        processing_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Multiple calls method successful!")
            print(f"⏱️  Processing time: {processing_time:.2f}s")
            print(f"🔢 Total LLM calls: {result['extraction_stats']['total_calls']}")
            print(f"📊 Overall confidence: {result['overall_confidence']:.2f}")
            print(f"📝 Sections found: {result['extraction_stats']['sections_found']}")
            
            # Show section summary
            print("\n📋 Section Extraction Summary:")
            for section, content in result['sections_extracted'].items():
                confidence = result['confidence_scores'].get(section, 0.0)
                status = "✅" if content and content.strip() and not content.startswith("ERROR:") and content != "NOT_FOUND" else "❌"
                content_preview = content[:50] + "..." if len(content) > 50 else content
                print(f"   {status} {section.capitalize()}: {confidence:.2f} - {content_preview}")
            
            if result['errors']:
                print(f"\n⚠️  Errors encountered: {len(result['errors'])}")
                for error in result['errors']:
                    print(f"   - {error}")
        else:
            print(f"❌ Multiple calls method failed: {response.status_code}")
            print(f"Error: {response.text}")
    
    except Exception as e:
        print(f"❌ Multiple calls method error: {e}")
    
    print("\n" + "=" * 60)
    
    # Test 2: Single call method (/section2)
    print("2. Testing Single Call Method (/section2)")
    print("-" * 50)
    
    try:
        start_time = time.time()
        
        with open(test_file, 'rb') as f:
            files = {'file': (test_file.name, f, 'application/pdf' if test_file.suffix == '.pdf' else 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')}
            response = requests.post(f"{BASE_URL}/section2", files=files, timeout=300)
        
        processing_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Single call method successful!")
            print(f"⏱️  Processing time: {processing_time:.2f}s")
            print(f"🔢 Total LLM calls: {result['extraction_stats']['total_calls']}")
            print(f"📊 Overall confidence: {result['overall_confidence']:.2f}")
            print(f"📝 Sections found: {result['extraction_stats']['sections_found']}")
            
            # Show section summary
            print("\n📋 Section Extraction Summary:")
            for section, content in result['sections_extracted'].items():
                confidence = result['confidence_scores'].get(section, 0.0)
                status = "✅" if content and content.strip() and not content.startswith("ERROR:") else "❌"
                content_preview = content[:50] + "..." if len(content) > 50 else content
                print(f"   {status} {section.capitalize()}: {confidence:.2f} - {content_preview}")
        else:
            print(f"❌ Single call method failed: {response.status_code}")
            print(f"Error: {response.text}")
    
    except Exception as e:
        print(f"❌ Single call method error: {e}")
    
    print("\n" + "=" * 60)

def check_extracted_files():
    """Check the extracted section files."""
    print("3. Checking Extracted Section Files")
    print("-" * 50)
    
    sections_dir = Path("resume sections extracted")
    if sections_dir.exists():
        section_files = list(sections_dir.glob("*.txt"))
        print(f"📁 Found {len(section_files)} section extraction files")
        
        if section_files:
            # Show recent files
            recent_files = sorted(section_files, key=lambda x: x.stat().st_mtime, reverse=True)[:3]
            print("\n📄 Recent extraction files:")
            for file in recent_files:
                size = file.stat().st_size
                mtime = time.ctime(file.stat().st_mtime)
                print(f"   - {file.name} ({size} bytes, {mtime})")
            
            # Show content of the most recent file
            if recent_files:
                latest_file = recent_files[0]
                print(f"\n📖 Content preview of {latest_file.name}:")
                print("-" * 40)
                try:
                    with open(latest_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        # Show first 800 characters
                        if len(content) > 800:
                            print(content[:800] + "\n... (truncated)")
                        else:
                            print(content)
                except Exception as e:
                    print(f"❌ Error reading file: {e}")
    else:
        print("❌ No 'resume sections extracted' directory found")

def compare_methods():
    """Compare the two extraction methods."""
    print("4. Method Comparison")
    print("-" * 50)
    
    print("📊 Multiple Calls Method (/section):")
    print("   ✅ Pros:")
    print("      - More focused prompts per section")
    print("      - Individual confidence scores")
    print("      - Better error isolation")
    print("      - Potentially higher accuracy per section")
    print("   ❌ Cons:")
    print("      - More API calls (higher cost)")
    print("      - Longer processing time")
    print("      - More complex error handling")
    
    print("\n📊 Single Call Method (/section2):")
    print("   ✅ Pros:")
    print("      - Faster processing (1 call)")
    print("      - Lower cost")
    print("      - Simpler implementation")
    print("      - Context awareness across sections")
    print("   ❌ Cons:")
    print("      - May miss some sections")
    print("      - Less focused extraction")
    print("      - Harder to debug failures")

if __name__ == "__main__":
    print("🚀 Section Extraction Testing Suite")
    print("Make sure the API server is running on http://localhost:8000")
    print()
    
    # Wait for user confirmation
    input("Press Enter to start testing...")
    
    # Run the tests
    test_section_extraction()
    
    # Check extracted files
    check_extracted_files()
    
    # Show comparison
    compare_methods()
    
    print("\n✨ Testing completed!")
    print("Check the 'resume sections extracted' folder for detailed results.")
