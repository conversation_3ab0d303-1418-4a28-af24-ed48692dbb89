#!/usr/bin/env python3
"""
Test script for /intervet_new endpoint using real resume and JD files
This script parses actual files and tests the CGPA-style scoring
"""

import requests
import json
import time
from pathlib import Path

# Configuration
API_BASE_URL = "http://localhost:8000"
RESUME_ENDPOINT = f"{API_BASE_URL}/resume"
JD_ENDPOINT = f"{API_BASE_URL}/jd_parser"
INTERVET_NEW_ENDPOINT = f"{API_BASE_URL}/intervet_new"

# Test files
RESUME_FILE = "resumes for testing/Resume-Raman Luhach.pdf"
JD_FILE = None  # We'll create a sample JD since no JD folder exists

def create_sample_jd_file():
    """Create a sample JD for testing"""
    jd_content = """
Job Title: Senior Full Stack Developer

Company: TechCorp Solutions
Location: San Francisco, CA

Job Description:
We are seeking a Senior Full Stack Developer to join our dynamic team. The ideal candidate will have strong experience in both frontend and backend development.

Required Skills:
- Python (3+ years)
- JavaScript (3+ years)
- React.js (2+ years)
- Node.js (2+ years)
- SQL databases (MySQL, PostgreSQL)
- REST API development
- Git version control
- AWS cloud services

Preferred Skills:
- Docker and containerization
- Kubernetes
- Machine Learning
- TypeScript
- GraphQL
- Microservices architecture

Experience Required:
- 5+ years of software development experience
- 3+ years of full-stack development
- Experience with agile development methodologies

Education:
- Bachelor's degree in Computer Science, Engineering, or related field
- Master's degree preferred

Responsibilities:
- Design and develop scalable web applications
- Collaborate with cross-functional teams
- Implement best practices for code quality
- Mentor junior developers
- Participate in code reviews

Benefits:
- Competitive salary
- Health insurance
- 401k matching
- Flexible work arrangements
"""
    
    # Save to a temporary file
    jd_file_path = "testing/sample_jd.txt"
    with open(jd_file_path, "w", encoding="utf-8") as f:
        f.write(jd_content)
    
    return jd_file_path

def parse_resume_file(file_path):
    """Parse resume file using the /resume endpoint"""
    print(f"📄 Parsing resume file: {file_path}")
    
    try:
        with open(file_path, 'rb') as f:
            files = {'file': (Path(file_path).name, f, 'application/pdf')}
            response = requests.post(RESUME_ENDPOINT, files=files, timeout=180)
        
        if response.status_code == 200:
            resume_data = response.json()
            print(f"✅ Resume parsed successfully")
            print(f"   Name: {resume_data.get('name', 'Unknown')}")
            print(f"   Skills: {len(resume_data.get('skills', []))} skills found")
            print(f"   Experience: {len(resume_data.get('experience', []))} entries")
            print(f"   Education: {len(resume_data.get('education', []))} entries")
            return resume_data
        else:
            print(f"❌ Resume parsing failed: {response.status_code}")
            print(f"Error: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error parsing resume: {e}")
        return None

def parse_jd_text(jd_text):
    """Parse JD text using the /jd_parser endpoint"""
    print(f"📋 Parsing job description...")
    
    try:
        # Create a temporary file for JD parsing
        temp_jd_file = "testing/temp_jd.txt"
        with open(temp_jd_file, "w", encoding="utf-8") as f:
            f.write(jd_text)
        
        with open(temp_jd_file, 'rb') as f:
            files = {'file': ('job_description.txt', f, 'text/plain')}
            response = requests.post(JD_ENDPOINT, files=files, timeout=180)
        
        # Clean up temp file
        Path(temp_jd_file).unlink(missing_ok=True)
        
        if response.status_code == 200:
            jd_data = response.json()
            print(f"✅ JD parsed successfully")
            print(f"   Job Title: {jd_data.get('job_title', 'Unknown')}")
            print(f"   Required Skills: {len(jd_data.get('required_skills', []))} skills")
            print(f"   Preferred Skills: {len(jd_data.get('preferred_skills', []))} skills")
            print(f"   Location: {jd_data.get('location', 'Not specified')}")
            return jd_data
        else:
            print(f"❌ JD parsing failed: {response.status_code}")
            print(f"Error: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error parsing JD: {e}")
        return None

def test_intervet_new_with_parsed_data(resume_data, jd_data, weightage_config=None):
    """Test the /intervet_new endpoint with parsed data"""
    print(f"\n🧪 Testing /intervet_new endpoint...")
    
    request_data = {
        "resume_json": resume_data,
        "jd_json": jd_data
    }
    
    if weightage_config:
        request_data["weightage"] = weightage_config
        print(f"   Using custom weightage: {weightage_config}")
    else:
        print(f"   Using default weightage")
    
    try:
        start_time = time.time()
        response = requests.post(INTERVET_NEW_ENDPOINT, json=request_data, timeout=120)
        processing_time = time.time() - start_time
        
        print(f"⏱️  Processing time: {processing_time:.2f} seconds")
        print(f"📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ CGPA-style evaluation completed successfully!")
            print(f"\n📈 FINAL RESULTS:")
            print(f"   🎯 Total Score: {result['total_score']:.2f}/10")
            print(f"   📊 Fit Category: {result['fit_category']}")
            print(f"   ⚖️  Total Credits Used: {result['total_credits_used']}")
            print(f"   ⏱️  Processing Time: {result['processing_time']:.2f}s")
            
            print(f"\n💭 Summary:")
            print(f"   {result['summary']}")
            
            print(f"\n📋 DETAILED FIELD SCORES:")
            fields = [
                ('Skills', result['skills_score']),
                ('Experience', result['experience_score']),
                ('Education', result['education_score']),
                ('Certifications', result['certifications_score']),
                ('Location', result['location_score']),
                ('Reliability', result['reliability_score'])
            ]
            
            for field_name, field_data in fields:
                print(f"\n   {field_name}:")
                print(f"     Raw Score: {field_data['raw_score']:.2f}/10")
                print(f"     Weight: {field_data['weight']}")
                print(f"     Weighted Score: {field_data['weighted_score']:.2f}")
                print(f"     Rationale: {field_data['rationale']}")
            
            return True
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing /intervet_new with Real Files")
    print("=" * 50)
    
    # Check if API is running
    try:
        response = requests.get(API_BASE_URL, timeout=10)
        if response.status_code != 200:
            print("❌ API server is not running. Please start the server first.")
            return
    except Exception:
        print("❌ Cannot connect to API server. Please start the server first.")
        return
    
    print("✅ API server is running")
    
    # Check if resume file exists
    if not Path(RESUME_FILE).exists():
        print(f"❌ Resume file not found: {RESUME_FILE}")
        print("Please ensure the resume file exists in the specified path.")
        return
    
    # Create sample JD
    print("\n📝 Creating sample job description...")
    jd_file_path = create_sample_jd_file()
    
    with open(jd_file_path, "r", encoding="utf-8") as f:
        jd_text = f.read()
    
    print("✅ Sample JD created")
    
    # Parse resume
    print(f"\n📄 Step 1: Parsing Resume")
    print("-" * 30)
    resume_data = parse_resume_file(RESUME_FILE)
    
    if not resume_data:
        print("❌ Failed to parse resume. Cannot proceed.")
        return
    
    # Parse JD
    print(f"\n📋 Step 2: Parsing Job Description")
    print("-" * 30)
    jd_data = parse_jd_text(jd_text)
    
    if not jd_data:
        print("❌ Failed to parse JD. Cannot proceed.")
        return
    
    # Test with default weights
    print(f"\n🧪 Step 3: Testing with Default Weights")
    print("-" * 30)
    success1 = test_intervet_new_with_parsed_data(resume_data, jd_data)
    
    # Test with custom weights (skills-heavy)
    print(f"\n🧪 Step 4: Testing with Skills-Heavy Weights")
    print("-" * 30)
    skills_heavy_weights = {
        "skills": 4.0,
        "experience": 2.0,
        "education": 1.5,
        "certifications": 1.0,
        "location": 1.0,
        "reliability": 0.5
    }
    success2 = test_intervet_new_with_parsed_data(resume_data, jd_data, skills_heavy_weights)
    
    # Test with custom weights (experience-heavy)
    print(f"\n🧪 Step 5: Testing with Experience-Heavy Weights")
    print("-" * 30)
    experience_heavy_weights = {
        "skills": 2.0,
        "experience": 4.0,
        "education": 2.0,
        "certifications": 1.0,
        "location": 1.0,
        "reliability": 0.0  # Disable reliability
    }
    success3 = test_intervet_new_with_parsed_data(resume_data, jd_data, experience_heavy_weights)
    
    # Summary
    print(f"\n📊 TEST SUMMARY")
    print("=" * 50)
    tests_passed = sum([success1, success2, success3])
    print(f"Tests passed: {tests_passed}/3")
    
    if tests_passed == 3:
        print("🎉 All tests passed! The /intervet_new endpoint is working correctly with real files.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
    
    print(f"\n📁 Check the 'intervet_new_logs' folder for detailed calculation logs.")
    print(f"📄 Sample JD file created at: {jd_file_path}")
    
    # Clean up
    Path(jd_file_path).unlink(missing_ok=True)

if __name__ == "__main__":
    main()
