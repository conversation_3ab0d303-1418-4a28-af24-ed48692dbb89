
INTERVET_NEW CALCULATION SUMMARY
================================
Timestamp: 20250702_154017_734
Processing Time: 0.003 seconds

FINAL RESULT
============
Total Score: 7.38/10
Fit Category: Strong Match
Summary: The candidate is a strong match for this position with a CGPA-style score of 7.4/10. Key strengths: Experience, Education. Areas for improvement: Certifications.

WEIGHTAGE CONFIGURATION
=======================
Skills: 4.0
Experience: 3.0
Education: 2.0
Certifications: 0.5
Location: 0.5
Reliability: 0.0
Total Credits: 10.0

DETAILED FIELD SCORES
=====================
Skills:
  Raw Score: 5.33/10
  Weight: 4.0
  Weighted Score: 21.33
  Rationale: Matched 2/3 required skills. Matched: Python, SQL. Missing: JavaScript

Experience:
  Raw Score: 10.00/10
  Weight: 3.0
  Weighted Score: 30.00
  Rationale: Excellent match: 2 years vs required 2 years (ratio: 1.00)

Education:
  Raw Score: 10.00/10
  Weight: 2.0
  Weighted Score: 20.00
  Rationale: Education requirements fully met: 'B.Tech Computer Science' matches requirement 'Bachelor's degree in Computer Science or related field' (Match type: exact_match)

Certifications:
  Raw Score: 0.00/10
  Weight: 0.5
  Weighted Score: 0.00
  Rationale: No certifications found in resume

Location:
  Raw Score: 5.00/10
  Weight: 0.5
  Weighted Score: 2.50
  Rationale: Location information not available for comparison

Reliability:
  Raw Score: 7.00/10
  Weight: 0.0
  Weighted Score: 0.00
  Rationale: Good stability: average 2.0 years per company

CALCULATION FORMULA
===================
Final Score = (Sum of Weighted Scores) / (Sum of Weights)
Final Score = (21.33 + 30.00 + 20.00 + 0.00 + 2.50 + 0.00) / 10.0
Final Score = 7.38/10
