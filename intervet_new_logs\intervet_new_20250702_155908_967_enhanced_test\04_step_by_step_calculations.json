{"timestamp": "20250702_155908_967", "calculation_transparency": "This file contains detailed step-by-step calculations for each scoring field", "field_calculations": {"skills": {"calculation_steps": ["Step 1: Required skills score = 8/8 × 8 = 8.00", "Step 2: Preferred skills bonus = 2/4 × 2 = 1.00", "Step 3: Total score = min(10, 8.00 + 1.00) = 9.00"], "scoring_formula": "Score = min(10, (matched_required/total_required × 8) + (matched_preferred/total_preferred × 2))", "explanation": "Required skills contribute up to 8 points (80%), preferred skills add up to 2 bonus points (20%)", "matching_details": []}, "experience": {"calculation_steps": ["Step 1: Required experience extracted: 3 years from '3+ years of software development experience'", "Step 2: Analyzing 3 experience entries", "  Entry 1: Tech Corp - Senior Software Engineer (2020 - Present) = 4 years", "  Entry 2: StartupXYZ - Full Stack Developer (2018 - 2020) = 2 years", "  Entry 3: WebDev Inc - Junior Developer (2016 - 2018) = 2 years", "Step 2 Result: Total candidate experience = 8 years", "Step 3: Calculating experience score", "  Experience ratio: 8/3 = 2.67", "  ~ Significantly over-experienced (>2.5): Score = 6.0"], "scoring_formula": "Score based on experience ratio: 10 for 0.8-1.5x, 7-10 for 0.6-0.8x, 8 for 1.5-2.5x, 6 for >2.5x, 2-6 for <0.6x", "explanation": "Experience scoring rewards candidates with appropriate experience levels, penalizing both under and over-qualification", "experience_breakdown": [{"company": "Tech Corp", "position": "Senior Software Engineer", "duration": "2020 - Present", "years_calculated": 4, "calculation_method": "Year range: 2020 to 2024"}, {"company": "StartupXYZ", "position": "Full Stack Developer", "duration": "2018 - 2020", "years_calculated": 2, "calculation_method": "Year range: 2018 to 2020"}, {"company": "WebDev Inc", "position": "Junior Developer", "duration": "2016 - 2018", "years_calculated": 2, "calculation_method": "Year range: 2016 to 2018"}]}, "education": {"calculation_steps": ["Step 1: Checking 1 education requirement(s)", "  - Analyzing requirement: 'Bachelor's degree in Computer Science or related field'", "    Required degree type: bachelor", "    Required field: computer science", "    Candidate degree: 'Bachelor of Science in Computer Science' (Type: bachelor, Field: computer science)", "    ✓ EXACT MATCH FOUND: Degree type and field match", "Step 2: Applying binary scoring system", "  ✓ Education requirement met: Score = 10.0"], "scoring_formula": "Binary scoring: 10 for exact match, 6 for partial match (benefit of doubt), 0 for no match, 5 for no requirements", "explanation": "Education scoring uses binary logic: candidates either meet the requirements (10 points) or don't (0 points), with benefit of doubt for related degrees (6 points)", "match_type": "exact_match"}, "certifications": {"calculation_steps": ["Step 1: Found 2 certifications in resume", "Step 2: Checking relevance against 12 job skills (8 required + 4 preferred)", "  Cert 1: 'AWS Certified Developer' - RELEVANT (matches: AWS) = +2 points", "  Cert 2: 'React Developer Certification' - RELEVANT (matches: React) = +2 points", "Step 3: Score calculation", "  Base score: 2 relevant certs × 2 points = 4", "  Final score: min(10, 4) = 4"], "scoring_formula": "Score = min(10, relevant_certifications_count × 2)", "explanation": "Each relevant certification (matching job skills) contributes 2 points, with a maximum score of 10", "certification_analysis": [{"certification": "AWS Certified Developer", "relevant": true, "matched_skills": ["AWS"], "points_awarded": 2}, {"certification": "React Developer Certification", "relevant": true, "matched_skills": ["React"], "points_awarded": 2}]}, "location": {"calculation_steps": ["Step 1: Location extraction", "  Job location: 'san francisco, ca' (from JD)", "  Resume location: 'san francisco, ca' (from resume)", "  Experience locations: ['san francisco, ca', 'san francisco, ca', 'san jose, ca'] (from work history)", "Step 2: Location matching analysis", "  ✓ Current location match: Score = 10.0"], "scoring_formula": "10 for current location match, 7 for previous work location match, 3 for no match, 5 for insufficient data", "explanation": "Location scoring prioritizes current location match, gives credit for previous work experience in the job location"}, "reliability": {"calculation_steps": ["Step 1: Analyzing 3 experience entries for tenure calculation", "  Entry 1: Tech Corp (2020 - Present) = 4 years", "  Entry 2: StartupXYZ (2018 - 2020) = 2 years", "  Entry 3: WebDev Inc (2016 - 2018) = 2 years", "Step 1 Result: Total experience = 8 years", "Step 2: Calculating job stability/reliability", "  Total companies: 3", "  Total years: 8", "  Average tenure: 8/3 = 2.67 years per company", "  ~ Good stability (2-3 years): Score = 7.0"], "scoring_formula": "Score based on average tenure: 10 for ≥4 years, 8.5 for 3-4 years, 7 for 2-3 years, 5 for 1.5-2 years, 3 for 1-1.5 years, 1 for <1 year", "explanation": "Reliability scoring measures job stability through average tenure per company, rewarding consistent employment history", "tenure_breakdown": [{"company": "Tech Corp", "duration": "2020 - Present", "years_calculated": 4, "calculation_method": "Year range: 2020 to 2024"}, {"company": "StartupXYZ", "duration": "2018 - 2020", "years_calculated": 2, "calculation_method": "Year range: 2018 to 2020"}, {"company": "WebDev Inc", "duration": "2016 - 2018", "years_calculated": 2, "calculation_method": "Year range: 2016 to 2018"}]}}, "final_calculation": {"formula": "Final Score = (Sum of Weighted Scores) / (Sum of Weights)", "calculation": "(45.00 + 18.00 + 20.00 + 6.00 + 10.00 + 3.50) / 13.0", "result": "7.88/10"}}