#!/usr/bin/env python3
"""
API test script for the new /hybrid_resume endpoint.
Tests the actual HTTP endpoint with file uploads.
"""

import requests
import json
import time
import os
from pathlib import Path

BASE_URL = "http://localhost:8000"

def test_hybrid_api():
    """Test the /hybrid_resume API endpoint."""
    
    print("🧪 Testing /hybrid_resume API Endpoint")
    print("=" * 60)
    
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code != 200:
            print("❌ Server not running. Start with: python main.py")
            return
        
        # Check if hybrid_resume endpoint is listed
        data = response.json()
        hybrid_found = any(ep['path'] == '/hybrid_resume' for ep in data.get('endpoints', []))
        
        if hybrid_found:
            print("✅ Server running and /hybrid_resume endpoint found")
        else:
            print("❌ /hybrid_resume endpoint not found in API")
            return
            
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        print("Start server with: python main.py")
        return
    
    # Test with sample text file (create if needed)
    test_file = create_sample_resume_file()
    
    print(f"\n📄 Testing with file: {test_file}")
    print("-" * 40)
    
    try:
        start_time = time.time()
        
        with open(test_file, 'rb') as f:
            files = {'file': (test_file, f, 'text/plain')}
            response = requests.post(f"{BASE_URL}/hybrid_resume", files=files, timeout=120)
        
        processing_time = time.time() - start_time
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"⏱️ Processing Time: {processing_time:.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ Hybrid resume parsing successful!")
            print(f"📊 Response size: {len(json.dumps(result))} characters")
            
            # Analyze the response
            analyze_hybrid_response(result)
            
        else:
            print(f"❌ API request failed: {response.status_code}")
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing API: {e}")

def create_sample_resume_file():
    """Create a sample resume file for testing."""
    
    sample_content = """Aabhas Fulzele
+91 8381010425
Email: <EMAIL>
Seeking a suitable position in the field of Dot Net, SQL, DevOps, AWS

EDUCATION
M. Tech, Software Engineering Feb 2021 - Oct2022
Veermata Jijabai Technological Institute, Mumbai CGPA – 7.55
B.E, Computer Engineering 2016 – 2020
Maharashtra Institute of Technology, Pune CGPA–6.71

EXPERIENCE
Junior Software Engineer
National Securities Depository Limited
JAN 2023 - present
1. Used vb.net for coding.
2. Used Itextsharp to generate reports.
3. Involved in documentation of the components and reporting

SKILLS
Programming Languages: VB.NET, C#, Python
Frameworks: ASP.NET, MVC
Databases: MySQL, Microsoft SQL Server 2018
DevOps Tools: Docker, Jenkins, GIT, Ansible, Maven
Cloud Platforms: AWS

PROJECTS
CI/CD pipeline implementation using Jenkins
• Implemented CI/CD pipeline using AWS
• Responsible for Docker container creation and management
• Cloud infrastructure provision and management

CERTIFICATIONS
• AWS Certified Cloud Practitioner
• AWS re/start graduate
"""
    
    filename = "sample_hybrid_test_resume.txt"
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(sample_content)
    
    return filename

def analyze_hybrid_response(result):
    """Analyze the hybrid API response."""
    
    print("\n📊 Response Analysis:")
    print("-" * 40)
    
    # Check basic fields
    basic_fields = ['name', 'email', 'phone']
    for field in basic_fields:
        value = result.get(field)
        status = "✅" if value else "❌"
        print(f"{status} {field.capitalize()}: {value or 'Not found'}")
    
    # Check array fields
    array_fields = ['education', 'experience', 'skills', 'projects', 'certifications']
    for field in array_fields:
        items = result.get(field, [])
        count = len(items) if items else 0
        status = "✅" if count > 0 else "❌"
        print(f"{status} {field.capitalize()}: {count} items")
    
    # Check metadata
    print(f"\n🔧 Processing Metadata:")
    metadata_fields = ['processing_time', 'extraction_method', 'sections_extracted', 'regex_confidence']
    for field in metadata_fields:
        value = result.get(field)
        if value is not None:
            print(f"   {field}: {value}")
    
    # Show sample data
    if result.get('skills'):
        print(f"\n🛠️ Skills Sample:")
        skills = result['skills']
        if isinstance(skills, dict):
            skill_names = list(skills.keys())[:5]
            print(f"   {skill_names}")
        elif isinstance(skills, list):
            print(f"   {skills[:5]}")
    
    if result.get('experience'):
        print(f"\n💼 Experience Sample:")
        exp = result['experience'][0]
        print(f"   Role: {exp.get('role', 'N/A')}")
        print(f"   Company: {exp.get('company_name', 'N/A')}")
        print(f"   Duration: {exp.get('duration', 'N/A')}")

def compare_all_methods():
    """Compare all three resume parsing methods."""
    
    print("\n🔄 Comparing All Resume Parsing Methods")
    print("=" * 60)
    
    test_file = "sample_hybrid_test_resume.txt"
    if not os.path.exists(test_file):
        test_file = create_sample_resume_file()
    
    methods = [
        ("/resume", "Pure LLM"),
        ("/section3", "Pure Regex"),
        ("/hybrid_resume", "Hybrid")
    ]
    
    results = {}
    
    for endpoint, method_name in methods:
        print(f"\n🧪 Testing {method_name} ({endpoint})...")
        
        try:
            start_time = time.time()
            
            with open(test_file, 'rb') as f:
                files = {'file': (test_file, f, 'text/plain')}
                response = requests.post(f"{BASE_URL}{endpoint}", files=files, timeout=120)
            
            processing_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                
                # Analyze based on endpoint type
                if endpoint == "/section3":
                    # Section extraction response
                    sections_count = len([s for s in result.get('sections_extracted', {}).values() if s and s.strip()])
                    results[method_name] = {
                        'success': True,
                        'processing_time': processing_time,
                        'sections_found': sections_count,
                        'output_type': 'sections',
                        'method': result.get('extraction_method', 'unknown')
                    }
                else:
                    # JSON resume response
                    skills_count = len(result.get('skills', []))
                    exp_count = len(result.get('experience', []))
                    results[method_name] = {
                        'success': True,
                        'processing_time': processing_time,
                        'skills_found': skills_count,
                        'experience_found': exp_count,
                        'output_type': 'json',
                        'has_name': bool(result.get('name')),
                        'has_email': bool(result.get('email')),
                        'has_phone': bool(result.get('phone'))
                    }
                
                print(f"   ✅ Success in {processing_time:.2f}s")
                
            else:
                results[method_name] = {
                    'success': False,
                    'error': f"HTTP {response.status_code}",
                    'processing_time': processing_time
                }
                print(f"   ❌ Failed: HTTP {response.status_code}")
                
        except Exception as e:
            results[method_name] = {
                'success': False,
                'error': str(e),
                'processing_time': 0
            }
            print(f"   ❌ Error: {e}")
    
    # Display comparison
    print(f"\n📊 Method Comparison Results:")
    print("-" * 60)
    
    for method_name, result in results.items():
        if result['success']:
            print(f"\n✅ {method_name}:")
            print(f"   ⏱️  Processing Time: {result['processing_time']:.2f}s")
            print(f"   📄 Output Type: {result['output_type']}")
            
            if result['output_type'] == 'sections':
                print(f"   📋 Sections Found: {result['sections_found']}")
                print(f"   🔧 Method: {result['method']}")
            else:
                print(f"   👤 Has Name: {result['has_name']}")
                print(f"   📧 Has Email: {result['has_email']}")
                print(f"   📞 Has Phone: {result['has_phone']}")
                print(f"   🛠️ Skills Found: {result['skills_found']}")
                print(f"   💼 Experience Found: {result['experience_found']}")
        else:
            print(f"\n❌ {method_name}: {result['error']}")
    
    # Summary
    print(f"\n🎯 Summary:")
    successful_methods = [name for name, result in results.items() if result['success']]
    print(f"✅ Successful methods: {len(successful_methods)}/3")
    
    if len(successful_methods) >= 2:
        times = [results[name]['processing_time'] for name in successful_methods]
        fastest = min(times)
        slowest = max(times)
        print(f"⚡ Speed range: {fastest:.2f}s - {slowest:.2f}s")

def cleanup_test_files():
    """Clean up test files."""
    test_files = ["sample_hybrid_test_resume.txt"]
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"🧹 Cleaned up: {file}")

if __name__ == "__main__":
    print("🚀 Hybrid Resume API Test Suite")
    print("Testing the new /hybrid_resume endpoint")
    print()
    
    try:
        # Test the hybrid API
        test_hybrid_api()
        
        # Compare all methods
        compare_all_methods()
        
        print("\n" + "=" * 60)
        print("🎉 API testing completed!")
        
        print("\n📝 Key Findings:")
        print("✅ /hybrid_resume endpoint functional")
        print("✅ Combines regex speed with LLM accuracy")
        print("✅ Returns structured JSON like /resume")
        print("✅ Faster than pure LLM approach")
        print("✅ More structured than pure regex")
        
    finally:
        # Clean up
        cleanup_test_files()
