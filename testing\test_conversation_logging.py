"""
Test script for conversation logging functionality.

This script tests the conversation logging feature that tracks all LLM calls
made during section extraction for the same resume.
"""

import requests
import json
import os
import time
from pathlib import Path

# API base URL
BASE_URL = "http://localhost:8000"

def test_conversation_logging():
    """Test the conversation logging functionality."""
    print("🧪 Testing Conversation Logging for Section Extraction")
    print("=" * 70)
    
    # Find test resume files
    resume_test_files = []
    test_dirs = ["resumes for testing", "resumes", "test_files"]
    
    for test_dir in test_dirs:
        if Path(test_dir).exists():
            resume_test_files.extend(list(Path(test_dir).glob("*.pdf")))
            resume_test_files.extend(list(Path(test_dir).glob("*.docx")))
    
    if not resume_test_files:
        print("❌ No test resume files found!")
        print("Please add some resume files to one of these directories:")
        for test_dir in test_dirs:
            print(f"   - {test_dir}/")
        return
    
    # Test with the first available resume
    test_file = resume_test_files[0]
    print(f"📄 Testing with file: {test_file.name}")
    print(f"📁 File size: {test_file.stat().st_size} bytes")
    print()
    
    # Test 1: Multiple calls method with conversation logging
    print("1. Testing Multiple Calls Method with Conversation Logging")
    print("-" * 60)
    
    try:
        start_time = time.time()
        
        with open(test_file, 'rb') as f:
            files = {'file': (test_file.name, f, 'application/pdf' if test_file.suffix == '.pdf' else 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')}
            response = requests.post(f"{BASE_URL}/section", files=files, timeout=300)
        
        processing_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Multiple calls method successful!")
            print(f"⏱️  Processing time: {processing_time:.2f}s")
            print(f"🔢 Total LLM calls: {result['extraction_stats']['total_calls']}")
            print(f"📊 Overall confidence: {result['overall_confidence']:.2f}")
            
            # Check conversation folder
            check_conversation_folder("multiple_calls", test_file.name)
        else:
            print(f"❌ Multiple calls method failed: {response.status_code}")
            print(f"Error: {response.text}")
    
    except Exception as e:
        print(f"❌ Multiple calls method error: {e}")
    
    print("\n" + "=" * 70)
    
    # Test 2: Single call method with conversation logging
    print("2. Testing Single Call Method with Conversation Logging")
    print("-" * 60)
    
    try:
        start_time = time.time()
        
        with open(test_file, 'rb') as f:
            files = {'file': (test_file.name, f, 'application/pdf' if test_file.suffix == '.pdf' else 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')}
            response = requests.post(f"{BASE_URL}/section2", files=files, timeout=300)
        
        processing_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Single call method successful!")
            print(f"⏱️  Processing time: {processing_time:.2f}s")
            print(f"🔢 Total LLM calls: {result['extraction_stats']['total_calls']}")
            print(f"📊 Overall confidence: {result['overall_confidence']:.2f}")
            
            # Check conversation folder
            check_conversation_folder("single_call", test_file.name)
        else:
            print(f"❌ Single call method failed: {response.status_code}")
            print(f"Error: {response.text}")
    
    except Exception as e:
        print(f"❌ Single call method error: {e}")

def check_conversation_folder(method_type, filename):
    """Check the conversation folder for logged calls."""
    print(f"\n📁 Checking conversation folder for {method_type}...")
    
    conv_dir = Path("conv")
    if not conv_dir.exists():
        print("❌ No 'conv' directory found")
        return
    
    # Find the most recent conversation folder for this method
    conv_folders = [f for f in conv_dir.iterdir() if f.is_dir() and method_type in f.name]
    if not conv_folders:
        print(f"❌ No conversation folders found for {method_type}")
        return
    
    # Get the most recent folder
    latest_folder = max(conv_folders, key=lambda x: x.stat().st_mtime)
    print(f"📂 Found conversation folder: {latest_folder.name}")
    
    # List files in the conversation folder
    files = list(latest_folder.glob("*.txt"))
    print(f"📄 Files in conversation folder: {len(files)}")
    
    for file in sorted(files):
        size = file.stat().st_size
        print(f"   - {file.name} ({size} bytes)")
    
    # Show content of conversation summary
    summary_file = latest_folder / "conversation_summary.txt"
    if summary_file.exists():
        print(f"\n📖 Conversation Summary:")
        print("-" * 40)
        try:
            with open(summary_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print(content[:500] + "..." if len(content) > 500 else content)
        except Exception as e:
            print(f"❌ Error reading summary: {e}")
    
    # Show content of final results summary
    final_summary_file = latest_folder / "final_results_summary.txt"
    if final_summary_file.exists():
        print(f"\n📊 Final Results Summary:")
        print("-" * 40)
        try:
            with open(final_summary_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print(content[:600] + "..." if len(content) > 600 else content)
        except Exception as e:
            print(f"❌ Error reading final summary: {e}")
    
    # Show content of one call file (if multiple calls)
    if method_type == "multiple_calls":
        call_files = [f for f in files if f.name.startswith("call_")]
        if call_files:
            sample_call = call_files[0]
            print(f"\n📝 Sample Call Log ({sample_call.name}):")
            print("-" * 40)
            try:
                with open(sample_call, 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(content[:400] + "..." if len(content) > 400 else content)
            except Exception as e:
                print(f"❌ Error reading call log: {e}")

def analyze_conversation_structure():
    """Analyze the overall conversation folder structure."""
    print("\n📁 Conversation Folder Structure Analysis")
    print("=" * 70)
    
    conv_dir = Path("conv")
    if not conv_dir.exists():
        print("❌ No 'conv' directory found")
        return
    
    conv_folders = [f for f in conv_dir.iterdir() if f.is_dir()]
    print(f"📂 Total conversation folders: {len(conv_folders)}")
    
    # Group by method type
    multiple_calls_folders = [f for f in conv_folders if "multiple_calls" in f.name]
    single_call_folders = [f for f in conv_folders if "single_call" in f.name]
    
    print(f"   - Multiple calls conversations: {len(multiple_calls_folders)}")
    print(f"   - Single call conversations: {len(single_call_folders)}")
    
    # Show recent conversations
    if conv_folders:
        print(f"\n📅 Recent conversations:")
        recent_folders = sorted(conv_folders, key=lambda x: x.stat().st_mtime, reverse=True)[:5]
        
        for folder in recent_folders:
            mtime = time.ctime(folder.stat().st_mtime)
            file_count = len(list(folder.glob("*.txt")))
            method = "multiple_calls" if "multiple_calls" in folder.name else "single_call"
            print(f"   - {folder.name}")
            print(f"     Method: {method}, Files: {file_count}, Modified: {mtime}")

def compare_conversation_logs():
    """Compare conversation logs between methods."""
    print("\n📊 Conversation Logs Comparison")
    print("=" * 70)
    
    conv_dir = Path("conv")
    if not conv_dir.exists():
        print("❌ No 'conv' directory found")
        return
    
    multiple_folders = [f for f in conv_dir.iterdir() if f.is_dir() and "multiple_calls" in f.name]
    single_folders = [f for f in conv_dir.iterdir() if f.is_dir() and "single_call" in f.name]
    
    print("📈 Method Comparison:")
    print(f"   Multiple Calls Method:")
    print(f"     - Conversations: {len(multiple_folders)}")
    if multiple_folders:
        latest_multi = max(multiple_folders, key=lambda x: x.stat().st_mtime)
        multi_files = len(list(latest_multi.glob("*.txt")))
        print(f"     - Files per conversation: ~{multi_files}")
        print(f"     - Latest: {latest_multi.name}")
    
    print(f"   Single Call Method:")
    print(f"     - Conversations: {len(single_folders)}")
    if single_folders:
        latest_single = max(single_folders, key=lambda x: x.stat().st_mtime)
        single_files = len(list(latest_single.glob("*.txt")))
        print(f"     - Files per conversation: ~{single_files}")
        print(f"     - Latest: {latest_single.name}")
    
    print(f"\n💡 Insights:")
    print(f"   - Multiple calls create more detailed logs (one per section)")
    print(f"   - Single calls create consolidated logs (all sections together)")
    print(f"   - Both methods include conversation summary and final results")

if __name__ == "__main__":
    print("🚀 Conversation Logging Testing Suite")
    print("Make sure the API server is running on http://localhost:8000")
    print()
    
    # Wait for user confirmation
    input("Press Enter to start testing...")
    
    # Run the tests
    test_conversation_logging()
    
    # Analyze conversation structure
    analyze_conversation_structure()
    
    # Compare conversation logs
    compare_conversation_logs()
    
    print("\n✨ Testing completed!")
    print("Check the 'conv' folder for detailed conversation logs.")
    print("Each conversation folder contains:")
    print("  - conversation_summary.txt (initial setup)")
    print("  - call_XX_section_name.txt (individual LLM calls)")
    print("  - final_results_summary.txt (final results)")
