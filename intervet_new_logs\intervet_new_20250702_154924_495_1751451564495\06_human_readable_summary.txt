
INTERVET_NEW CALCULATION SUMMARY
================================
Timestamp: 20250702_154924_495
Processing Time: 0.006 seconds

FINAL RESULT
============
Total Score: 5.84/10
Fit Category: Good Match
Summary: The candidate is a good match for this position with a CGPA-style score of 5.8/10. Key strengths: Education. Areas for improvement: Skills, Certifications.

WEIGHTAGE CONFIGURATION
=======================
Skills: 5.0
Experience: 3.0
Education: 5.0
Certifications: 0.0
Location: 0.0
Reliability: 0.0
Total Credits: 13.0

DETAILED FIELD SCORES
=====================
Skills:
  Raw Score: 2.18/10
  Weight: 5.0
  Weighted Score: 10.91
  Rationale: Matched 3/11 required skills and 0/7 preferred skills. Matched: Python, PostgreSQL, PyTorch. Missing: NLP, Generative AI, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Transformers

Experience:
  Raw Score: 5.00/10
  Weight: 3.0
  Weighted Score: 15.00
  Rationale: Could not determine candidate's experience to compare with required 2 years

Education:
  Raw Score: 10.00/10
  Weight: 5.0
  Weighted Score: 50.00
  Rationale: Education requirements fully met: 'B.Tech Computer Science Engineering (Artificial Intelligence & Machine Learning)' matches requirement 'Bachelor's degree in Computer Science, Engineering, Computational Linguistics, Data Science or a related quantitative field' (Match type: exact_match)

Certifications:
  Raw Score: 0.00/10
  Weight: 0.0
  Weighted Score: 0.00
  Rationale: No certifications found in resume

Location:
  Raw Score: 5.00/10
  Weight: 0.0
  Weighted Score: 0.00
  Rationale: Location information not available for comparison

Reliability:
  Raw Score: 5.00/10
  Weight: 0.0
  Weighted Score: 0.00
  Rationale: Could not calculate job stability due to missing experience data

CALCULATION FORMULA
===================
Final Score = (Sum of Weighted Scores) / (Sum of Weights)
Final Score = (10.91 + 15.00 + 50.00 + 0.00 + 0.00 + 0.00) / 13.0
Final Score = 5.84/10
