"""
Test script for prompt logging functionality.

This script tests the prompt logging system by making various API calls
and verifying that logs are created properly.
"""

import requests
import json
import os
import time
from pathlib import Path

# API base URL
BASE_URL = "http://localhost:8000"

def test_prompt_logging():
    """Test the prompt logging functionality."""
    print("🧪 Testing Prompt Logging Functionality")
    print("=" * 50)
    
    # Test 1: Simple generate endpoint
    print("\n1. Testing /generate endpoint...")
    try:
        response = requests.post(f"{BASE_URL}/generate", json={
            "prompt": "What is artificial intelligence?",
            "history": ""
        })
        if response.status_code == 200:
            print("✅ Generate endpoint test successful")
        else:
            print(f"❌ Generate endpoint test failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Generate endpoint test error: {e}")
    
    # Test 2: Resume parsing (if test files exist)
    print("\n2. Testing /resume endpoint...")
    resume_test_files = list(Path("resumes for testing").glob("*.pdf")) if Path("resumes for testing").exists() else []
    
    if resume_test_files:
        test_file = resume_test_files[0]
        try:
            with open(test_file, 'rb') as f:
                files = {'file': (test_file.name, f, 'application/pdf')}
                response = requests.post(f"{BASE_URL}/resume", files=files)
            
            if response.status_code == 200:
                print(f"✅ Resume parsing test successful with {test_file.name}")
            else:
                print(f"❌ Resume parsing test failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Resume parsing test error: {e}")
    else:
        print("⚠️ No resume test files found, skipping resume test")
    
    # Test 3: Job description parsing (if test files exist)
    print("\n3. Testing /jd_parser endpoint...")
    jd_test_files = list(Path("jds").glob("*.pdf")) if Path("jds").exists() else []
    
    if jd_test_files:
        test_file = jd_test_files[0]
        try:
            with open(test_file, 'rb') as f:
                files = {'file': (test_file.name, f, 'application/pdf')}
                response = requests.post(f"{BASE_URL}/jd_parser", files=files)
            
            if response.status_code == 200:
                print(f"✅ JD parsing test successful with {test_file.name}")
            else:
                print(f"❌ JD parsing test failed: {response.status_code}")
        except Exception as e:
            print(f"❌ JD parsing test error: {e}")
    else:
        print("⚠️ No JD test files found, skipping JD test")
    
    # Test 4: Check prompt log stats
    print("\n4. Testing prompt log stats...")
    try:
        response = requests.get(f"{BASE_URL}/prompt_logs/stats")
        if response.status_code == 200:
            stats = response.json()
            print("✅ Prompt log stats retrieved successfully")
            print(f"📊 Total log files: {stats['stats'].get('total_files', 0)}")
            print(f"📊 Total log size: {stats['stats'].get('total_size', 0)} bytes")
            
            # Show subdirectory breakdown
            subdirs = stats['stats'].get('subdirs', {})
            if subdirs:
                print("📁 Log subdirectories:")
                for subdir, info in subdirs.items():
                    print(f"   - {subdir}: {info['files']} files, {info['size']} bytes")
        else:
            print(f"❌ Prompt log stats test failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Prompt log stats test error: {e}")
    
    # Test 5: Check if log files were created
    print("\n5. Checking if log files were created...")
    prompt_logs_dir = Path("prompt_logs")
    
    if prompt_logs_dir.exists():
        log_files = list(prompt_logs_dir.rglob("*.txt"))
        print(f"✅ Found {len(log_files)} log files in prompt_logs directory")
        
        # Show recent log files
        if log_files:
            print("📄 Recent log files:")
            # Sort by modification time, show last 5
            recent_files = sorted(log_files, key=lambda x: x.stat().st_mtime, reverse=True)[:5]
            for log_file in recent_files:
                rel_path = log_file.relative_to(prompt_logs_dir)
                size = log_file.stat().st_size
                print(f"   - {rel_path} ({size} bytes)")
    else:
        print("❌ No prompt_logs directory found")
    
    print("\n" + "=" * 50)
    print("🏁 Prompt logging test completed!")

def show_sample_log():
    """Show a sample log file content."""
    print("\n📖 Sample Log File Content")
    print("=" * 50)
    
    prompt_logs_dir = Path("prompt_logs")
    if prompt_logs_dir.exists():
        log_files = list(prompt_logs_dir.rglob("*.txt"))
        if log_files:
            # Get the most recent log file
            recent_file = max(log_files, key=lambda x: x.stat().st_mtime)
            print(f"📄 Showing content of: {recent_file.relative_to(prompt_logs_dir)}")
            print("-" * 50)
            
            try:
                with open(recent_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # Show first 1000 characters
                    if len(content) > 1000:
                        print(content[:1000] + "\n... (truncated)")
                    else:
                        print(content)
            except Exception as e:
                print(f"❌ Error reading log file: {e}")
        else:
            print("❌ No log files found")
    else:
        print("❌ No prompt_logs directory found")

if __name__ == "__main__":
    print("🚀 Starting Prompt Logging Tests")
    print("Make sure the API server is running on http://localhost:8000")
    print()
    
    # Wait a moment for user to confirm
    input("Press Enter to continue with the tests...")
    
    # Run the tests
    test_prompt_logging()
    
    # Show a sample log
    show_sample_log()
    
    print("\n✨ All tests completed!")
