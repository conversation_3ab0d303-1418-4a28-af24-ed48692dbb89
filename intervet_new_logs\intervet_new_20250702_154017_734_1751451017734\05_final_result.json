{"total_score": 7.383333333333333, "fit_category": "Strong Match", "summary": "The candidate is a strong match for this position with a CGPA-style score of 7.4/10. Key strengths: Experience, Education. Areas for improvement: Certifications.", "skills_score": {"raw_score": 5.333333333333333, "normalized_score": 5.333333333333333, "weight": 4.0, "weighted_score": 21.333333333333332, "rationale": "Matched 2/3 required skills. Matched: Python, SQL. Missing: JavaScript", "details": {"resume_skills_count": 4, "resume_skills_list": ["Python", "Machine Learning", "FastAPI", "SQL"], "required_skills_count": 3, "required_skills_list": ["Python", "JavaScript", "SQL"], "preferred_skills_count": 0, "preferred_skills_list": [], "matched_required": ["Python", "SQL"], "missing_required": ["JavaScript"], "matched_preferred": [], "required_match_ratio": 0.6666666666666666, "preferred_match_ratio": 0, "calculation_steps": ["Step 1: Required skills score = 2/3 × 8 = 5.33", "Step 2: No preferred skills specified, bonus = 0.00", "Step 3: Total score = min(10, 5.33 + 0.00) = 5.33"], "scoring_formula": "Score = min(10, (matched_required/total_required × 8) + (matched_preferred/total_preferred × 2))", "explanation": "Required skills contribute up to 8 points (80%), preferred skills add up to 2 bonus points (20%)"}}, "experience_score": {"raw_score": 10.0, "normalized_score": 10.0, "weight": 3.0, "weighted_score": 30.0, "rationale": "Excellent match: 2 years vs required 2 years (ratio: 1.00)", "details": {"candidate_yoe": 2, "required_yoe": 2, "experience_ratio": 1.0, "experience_entries_count": 1, "experience_breakdown": [{"company": "Unknown Company", "position": "Unknown Position", "duration": "2022-Present", "years_calculated": 2, "calculation_method": "Year range: 2022 to 2024"}], "calculation_steps": ["Step 1: Required experience extracted: 2 years from '2+ years'", "Step 2: Analyzing 1 experience entries", "  Entry 1: Unknown Company - Unknown Position (2022-Present) = 2 years", "Step 2 Result: Total candidate experience = 2 years", "Step 3: Calculating experience score", "  Experience ratio: 2/2 = 1.00", "  ✓ Ideal range (0.8-1.5): Score = 10.0"], "scoring_formula": "Score based on experience ratio: 10 for 0.8-1.5x, 7-10 for 0.6-0.8x, 8 for 1.5-2.5x, 6 for >2.5x, 2-6 for <0.6x", "explanation": "Experience scoring rewards candidates with appropriate experience levels, penalizing both under and over-qualification"}}, "education_score": {"raw_score": 10.0, "normalized_score": 10.0, "weight": 2.0, "weighted_score": 20.0, "rationale": "Education requirements fully met: 'B.Tech Computer Science' matches requirement 'Bachelor's degree in Computer Science or related field' (Match type: exact_match)", "details": {"education_entries_count": 1, "education_requirements_count": 1, "education_match": true, "partial_match": false, "matched_degree": "B.Tech Computer Science", "matched_requirement": "Bachelor's degree in Computer Science or related field", "match_type": "exact_match", "candidate_degrees": ["B.Tech Computer Science"], "required_degrees": ["Bachelor's degree in Computer Science or related field"], "calculation_steps": ["Step 1: Checking 1 education requirement(s)", "  - Analyzing requirement: 'Bachelor's degree in Computer Science or related field'", "    Required degree type: bachelor", "    Required field: computer science", "    Candidate degree: 'B.Tech Computer Science' (Type: bachelor, Field: computer science)", "    ✓ EXACT MATCH FOUND: Degree type and field match", "Step 2: Applying binary scoring system", "  ✓ Education requirement met: Score = 10.0"], "scoring_formula": "Binary scoring: 10 for exact match, 6 for partial match (benefit of doubt), 0 for no match, 5 for no requirements", "explanation": "Education scoring uses binary logic: candidates either meet the requirements (10 points) or don't (0 points), with benefit of doubt for related degrees (6 points)"}}, "certifications_score": {"raw_score": 0.0, "normalized_score": 0.0, "weight": 0.5, "weighted_score": 0.0, "rationale": "No certifications found in resume", "details": {"total_certifications": 0, "relevant_certifications": [], "irrelevant_certifications": [], "relevant_count": 0, "all_certifications": [], "certification_analysis": [], "calculation_steps": ["Step 1: Found 0 certifications in resume", "Step 2: Checking relevance against 3 job skills (3 required + 0 preferred)", "Step 3: Score calculation", "  Base score: 0 relevant certs × 2 points = 0", "  Final score: min(10, 0) = 0"], "scoring_formula": "Score = min(10, relevant_certifications_count × 2)", "explanation": "Each relevant certification (matching job skills) contributes 2 points, with a maximum score of 10"}}, "location_score": {"raw_score": 5.0, "normalized_score": 5.0, "weight": 0.5, "weighted_score": 2.5, "rationale": "Location information not available for comparison", "details": {"jd_location": "", "resume_location": "", "experience_locations": [], "has_location_info": false, "calculation_steps": ["Step 1: Location extraction", "  Job location: '' (from JD)", "  Resume location: '' (from resume)", "  Experience locations: [] (from work history)", "Step 2: Location matching analysis", "  ~ Insufficient location data: Score = 5.0 (neutral)"], "scoring_formula": "10 for current location match, 7 for previous work location match, 3 for no match, 5 for insufficient data", "explanation": "Location scoring prioritizes current location match, gives credit for previous work experience in the job location"}}, "reliability_score": {"raw_score": 7.0, "normalized_score": 7.0, "weight": 0.0, "weighted_score": 0.0, "rationale": "Good stability: average 2.0 years per company", "details": {"candidate_yoe": 2, "num_companies": 1, "avg_tenure": 2.0, "has_experience_data": true, "tenure_breakdown": [{"company": "Unknown Company", "duration": "2022-Present", "years_calculated": 2, "calculation_method": "Year range: 2022 to 2024"}], "calculation_steps": ["Step 1: Analyzing 1 experience entries for tenure calculation", "  Entry 1: Unknown Company (2022-Present) = 2 years", "Step 1 Result: Total experience = 2 years", "Step 2: Calculating job stability/reliability", "  Total companies: 1", "  Total years: 2", "  Average tenure: 2/1 = 2.00 years per company", "  ~ Good stability (2-3 years): Score = 7.0"], "scoring_formula": "Score based on average tenure: 10 for ≥4 years, 8.5 for 3-4 years, 7 for 2-3 years, 5 for 1.5-2 years, 3 for 1-1.5 years, 1 for <1 year", "explanation": "Reliability scoring measures job stability through average tenure per company, rewarding consistent employment history"}}, "total_credits_used": 10.0, "calculation_method": "CGPA", "processing_time": 0.003000974655151367}