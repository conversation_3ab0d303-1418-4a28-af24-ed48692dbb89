#!/usr/bin/env python3
"""
Test the enhanced CGPA system with real resume and JD files.
This test uses actual files from the testing folders to validate the system.
"""

import sys
import os
import json
import time
import requests
from pathlib import Path

# Add the parent directory to the path so we can import from main
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from main import (
    WeightageConfig, 
    calculate_candidate_job_fit_new,
    log_intervet_new_calculation
)

def parse_resume_file(file_path):
    """Parse a resume file using the API endpoint"""
    try:
        with open(file_path, 'rb') as f:
            files = {'file': f}
            response = requests.post('http://localhost:8000/resume', files=files, timeout=30)
            
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ Failed to parse resume {file_path}: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Error parsing resume {file_path}: {e}")
        return None

def parse_jd_file(file_path):
    """Parse a JD file using the API endpoint"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            jd_text = f.read()
        
        response = requests.post(
            'http://localhost:8000/jd_parser',
            json={'jd_text': jd_text},
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ Failed to parse JD {file_path}: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Error parsing JD {file_path}: {e}")
        return None

def test_with_real_files():
    """Test enhanced CGPA system with real resume and JD files"""
    print("🧪 Testing Enhanced CGPA System with Real Files")
    print("=" * 60)
    
    # Get resume files
    resume_dir = Path("resumes for testing")
    jd_file = Path("testing/sample_jd.txt")
    
    if not resume_dir.exists():
        print("❌ Resume directory not found")
        return False
    
    if not jd_file.exists():
        print("❌ Sample JD file not found")
        return False
    
    # Parse the JD file
    print("📄 Parsing job description...")
    jd_data = parse_jd_file(jd_file)
    if not jd_data:
        print("❌ Failed to parse JD file")
        return False
    
    print(f"✅ JD parsed successfully")
    print(f"   Title: {jd_data.get('title', 'N/A')}")
    print(f"   Required Skills: {len(jd_data.get('required_skills', []))}")
    print(f"   Preferred Skills: {len(jd_data.get('preferred_skills', []))}")
    
    # Test with a few resume files
    resume_files = list(resume_dir.glob("*.pdf"))[:3]  # Test with first 3 PDF files
    
    if not resume_files:
        print("❌ No PDF resume files found")
        return False
    
    results = []
    
    for i, resume_file in enumerate(resume_files):
        print(f"\n📋 Testing Resume {i+1}: {resume_file.name}")
        print("-" * 50)
        
        # Parse resume
        resume_data = parse_resume_file(resume_file)
        if not resume_data:
            print(f"❌ Failed to parse resume {resume_file.name}")
            continue
        
        print(f"✅ Resume parsed successfully")
        print(f"   Name: {resume_data.get('name', 'N/A')}")
        print(f"   Skills: {len(resume_data.get('skills', []))}")
        print(f"   Experience: {len(resume_data.get('experience', []))} entries")
        print(f"   Education: {len(resume_data.get('education', []))} entries")
        
        # Test with different weight configurations
        weight_configs = [
            ("Default Weights", WeightageConfig()),
            ("Skills Heavy", WeightageConfig(skills=5.0, experience=2.0, education=2.0, certifications=1.0, location=0.5, reliability=0.5)),
            ("Experience Heavy", WeightageConfig(skills=2.0, experience=5.0, education=2.0, certifications=1.0, location=0.5, reliability=0.5))
        ]
        
        for config_name, weights in weight_configs:
            print(f"\n  🔧 Testing with {config_name}...")
            
            start_time = time.time()
            result = calculate_candidate_job_fit_new(resume_data, jd_data, weights)
            processing_time = time.time() - start_time
            
            print(f"     ⏱️  Processing Time: {processing_time:.3f} seconds")
            print(f"     📊 Total Score: {result.total_score:.2f}/10")
            print(f"     🎯 Fit Category: {result.fit_category}")
            print(f"     ⚖️  Total Credits: {result.total_credits_used}")
            
            # Verify enhanced features
            skills_steps = len(result.skills_score.details.get('calculation_steps', []))
            exp_steps = len(result.experience_score.details.get('calculation_steps', []))
            edu_steps = len(result.education_score.details.get('calculation_steps', []))
            
            print(f"     🔍 Calculation Steps: Skills({skills_steps}), Experience({exp_steps}), Education({edu_steps})")
            
            # Test enhanced logging for one configuration
            if config_name == "Default Weights":
                print(f"     📝 Testing enhanced logging...")
                log_folder = log_intervet_new_calculation(
                    resume_data, jd_data, weights, result, 
                    f"real_file_test_{resume_file.stem}"
                )
                
                if log_folder:
                    print(f"     ✅ Enhanced logs created: {log_folder}")
                    
                    # Check for new enhanced log files
                    enhanced_files = [
                        "04_step_by_step_calculations.json",
                        "07_detailed_calculation_explanation.txt"
                    ]
                    
                    for file_name in enhanced_files:
                        file_path = os.path.join(log_folder, file_name)
                        if os.path.exists(file_path):
                            print(f"       ✅ {file_name} created")
                        else:
                            print(f"       ❌ {file_name} missing")
                else:
                    print(f"     ❌ Enhanced logging failed")
            
            results.append({
                'resume': resume_file.name,
                'config': config_name,
                'score': result.total_score,
                'category': result.fit_category,
                'processing_time': processing_time
            })
    
    # Summary
    print(f"\n📊 Test Summary")
    print("=" * 60)
    print(f"Tested {len(resume_files)} resumes with {len(weight_configs)} weight configurations each")
    print(f"Total tests: {len(results)}")
    
    if results:
        avg_score = sum(r['score'] for r in results) / len(results)
        avg_time = sum(r['processing_time'] for r in results) / len(results)
        
        print(f"Average Score: {avg_score:.2f}/10")
        print(f"Average Processing Time: {avg_time:.3f} seconds")
        
        # Show score distribution
        categories = {}
        for result in results:
            cat = result['category']
            categories[cat] = categories.get(cat, 0) + 1
        
        print(f"\nScore Distribution:")
        for category, count in categories.items():
            print(f"  {category}: {count} tests")
    
    return True

def test_binary_education_with_real_data():
    """Test binary education scoring with real resume data"""
    print("\n🧪 Testing Binary Education Scoring with Real Data")
    print("=" * 60)
    
    # Create test cases for binary education scoring
    test_cases = [
        {
            "name": "CS Degree Required",
            "jd": {"education_requirements": ["Bachelor's degree in Computer Science"]},
            "description": "Testing with CS degree requirement"
        },
        {
            "name": "Engineering Degree Required", 
            "jd": {"education_requirements": ["Bachelor's degree in Engineering"]},
            "description": "Testing with Engineering degree requirement"
        },
        {
            "name": "Any Bachelor's Required",
            "jd": {"education_requirements": ["Bachelor's degree"]},
            "description": "Testing with general bachelor's requirement"
        }
    ]
    
    # Get a sample resume
    resume_dir = Path("resumes for testing")
    resume_files = list(resume_dir.glob("*.pdf"))[:1]  # Test with first resume
    
    if not resume_files:
        print("❌ No resume files found for testing")
        return False
    
    resume_file = resume_files[0]
    print(f"📋 Using resume: {resume_file.name}")
    
    # Parse resume
    resume_data = parse_resume_file(resume_file)
    if not resume_data:
        print("❌ Failed to parse resume")
        return False
    
    print(f"✅ Resume education entries: {len(resume_data.get('education', []))}")
    for edu in resume_data.get('education', []):
        print(f"   - {edu.get('degree', 'Unknown degree')}")
    
    # Test each case
    for test_case in test_cases:
        print(f"\n🔬 {test_case['name']}")
        print(f"   {test_case['description']}")
        
        from main import calculate_education_score_new
        score, rationale, details = calculate_education_score_new(resume_data, test_case['jd'])
        
        print(f"   Score: {score:.1f}/10")
        print(f"   Rationale: {rationale}")
        print(f"   Match Type: {details.get('match_type', 'N/A')}")
        print(f"   Binary Logic: {'✅ Working' if score in [0, 5, 6, 10] else '❌ Not Binary'}")
        
        # Show calculation steps
        steps = details.get('calculation_steps', [])
        if steps:
            print(f"   Calculation Steps ({len(steps)}):")
            for step in steps[:3]:  # Show first 3 steps
                print(f"     {step}")
            if len(steps) > 3:
                print(f"     ... and {len(steps) - 3} more steps")
    
    return True

def main():
    """Run all real file tests"""
    print("🚀 Enhanced CGPA System - Real File Test Suite")
    print("=" * 70)
    print("Testing enhanced CGPA system with actual resume and JD files")
    print("Validating: enhanced logging, binary education, detailed calculations")
    print("=" * 70)
    
    try:
        # Check if API server is running
        try:
            response = requests.get('http://localhost:8000/health', timeout=5)
            if response.status_code != 200:
                print("❌ API server not responding correctly")
                print("Please start the server with: python main.py")
                return False
        except:
            print("❌ API server not running")
            print("Please start the server with: python main.py")
            return False
        
        print("✅ API server is running")
        
        # Test with real files
        if not test_with_real_files():
            return False
        
        # Test binary education scoring
        if not test_binary_education_with_real_data():
            return False
        
        print("\n" + "=" * 70)
        print("🎉 All Real File Tests Completed Successfully!")
        print("✅ Enhanced CGPA system working with real data")
        print("✅ Binary education scoring validated")
        print("✅ Enhanced logging system functional")
        print("✅ Detailed calculation transparency confirmed")
        print("=" * 70)
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
