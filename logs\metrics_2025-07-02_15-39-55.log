{"event": "session_start", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "timestamp": "2025-07-02T15:39:55.493160", "message": "New API session started"}
{"event": "request_start", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "dacc648b-ff94-44c0-b1a7-55038d955dd4", "endpoint": "/", "timestamp": "2025-07-02T15:39:57.074840", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "dacc648b-ff94-44c0-b1a7-55038d955dd4", "endpoint": "/", "timestamp": "2025-07-02T15:39:57.076843", "total_time_seconds": 0.002002716064453125, "status_code": 200, "message": "Request completed in 0.0020s with status 200"}
{"event": "request_start", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "6eac4e13-cc36-44bb-8b49-11394c6d43a2", "endpoint": "/favicon.ico", "timestamp": "2025-07-02T15:39:57.138365", "message": "Request started for endpoint: /favicon.ico"}
{"event": "request_complete", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "6eac4e13-cc36-44bb-8b49-11394c6d43a2", "endpoint": "/favicon.ico", "timestamp": "2025-07-02T15:39:57.139362", "total_time_seconds": 0.000997304916381836, "status_code": 404, "message": "Request completed in 0.0010s with status 404"}
{"event": "request_start", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "31ce1910-332a-42b0-b3ff-01118a69b4a7", "endpoint": "/docs", "timestamp": "2025-07-02T15:40:01.004080", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "31ce1910-332a-42b0-b3ff-01118a69b4a7", "endpoint": "/docs", "timestamp": "2025-07-02T15:40:01.005082", "total_time_seconds": 0.0010023117065429688, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "0216c341-5800-4e90-b248-e2a2bd182d78", "endpoint": "/openapi.json", "timestamp": "2025-07-02T15:40:01.102351", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "0216c341-5800-4e90-b248-e2a2bd182d78", "endpoint": "/openapi.json", "timestamp": "2025-07-02T15:40:01.119358", "total_time_seconds": 0.017006635665893555, "status_code": 200, "message": "Request completed in 0.0170s with status 200"}
{"event": "request_start", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "a91e595c-f3da-4817-8058-da1068a7bd68", "endpoint": "/intervet_new", "timestamp": "2025-07-02T15:40:17.729439", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "a91e595c-f3da-4817-8058-da1068a7bd68", "endpoint": "/intervet_new", "timestamp": "2025-07-02T15:40:17.731438", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "a91e595c-f3da-4817-8058-da1068a7bd68", "endpoint": "/intervet_new", "timestamp": "2025-07-02T15:40:17.740438", "final_score": 7.383333333333333, "message": "Custom metric: final_score=7.383333333333333"}
{"event": "custom_metric", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "a91e595c-f3da-4817-8058-da1068a7bd68", "endpoint": "/intervet_new", "timestamp": "2025-07-02T15:40:17.740438", "fit_category": "Strong Match", "message": "Custom metric: fit_category=Strong Match"}
{"event": "custom_metric", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "a91e595c-f3da-4817-8058-da1068a7bd68", "endpoint": "/intervet_new", "timestamp": "2025-07-02T15:40:17.740438", "total_credits_used": 10.0, "message": "Custom metric: total_credits_used=10.0"}
{"event": "custom_metric", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "a91e595c-f3da-4817-8058-da1068a7bd68", "endpoint": "/intervet_new", "timestamp": "2025-07-02T15:40:17.740438", "log_folder": "intervet_new_logs\\intervet_new_20250702_154017_734_1751451017734", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250702_154017_734_1751451017734"}
{"event": "request_complete", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "a91e595c-f3da-4817-8058-da1068a7bd68", "endpoint": "/intervet_new", "timestamp": "2025-07-02T15:40:17.741438", "total_time_seconds": 0.011998891830444336, "status_code": 200, "message": "Request completed in 0.0120s with status 200"}
{"event": "request_start", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "4a80fcd9-2860-4de3-9a99-6133dd0814a0", "endpoint": "/hybrid_resume", "timestamp": "2025-07-02T15:43:45.948978", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "4a80fcd9-2860-4de3-9a99-6133dd0814a0", "endpoint": "/hybrid_resume", "timestamp": "2025-07-02T15:43:45.984264", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "4a80fcd9-2860-4de3-9a99-6133dd0814a0", "endpoint": "/hybrid_resume", "timestamp": "2025-07-02T15:43:45.984264", "file_size_bytes": 60820, "message": "Custom metric: file_size_bytes=60820"}
{"event": "custom_metric", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "4a80fcd9-2860-4de3-9a99-6133dd0814a0", "endpoint": "/hybrid_resume", "timestamp": "2025-07-02T15:43:45.984264", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "4a80fcd9-2860-4de3-9a99-6133dd0814a0", "endpoint": "/hybrid_resume", "timestamp": "2025-07-02T15:43:45.984264", "extracted_text_length": 2804, "message": "Custom metric: extracted_text_length=2804"}
{"event": "custom_metric", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "4a80fcd9-2860-4de3-9a99-6133dd0814a0", "endpoint": "/hybrid_resume", "timestamp": "2025-07-02T15:43:45.984264", "file_processing_time": 0.03028559684753418, "message": "Custom metric: file_processing_time=0.03028559684753418"}
{"event": "request_complete", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "4a80fcd9-2860-4de3-9a99-6133dd0814a0", "endpoint": "/hybrid_resume", "timestamp": "2025-07-02T15:44:21.556572", "total_time_seconds": 35.60759401321411, "status_code": 200, "message": "Request completed in 35.6076s with status 200"}
{"event": "request_start", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "3397ca05-e6ee-4cf5-8892-4636eb254573", "endpoint": "/jd_parser", "timestamp": "2025-07-02T15:44:21.558574", "message": "Request started for endpoint: /jd_parser"}
{"event": "request_complete", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "3397ca05-e6ee-4cf5-8892-4636eb254573", "endpoint": "/jd_parser", "timestamp": "2025-07-02T15:44:31.931808", "total_time_seconds": 10.373233795166016, "status_code": 200, "message": "Request completed in 10.3732s with status 200"}
{"event": "request_start", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "5a1a3d43-69a6-425c-bb59-444f70bc36dc", "endpoint": "/jd_parser", "timestamp": "2025-07-02T15:45:53.294273", "message": "Request started for endpoint: /jd_parser"}
{"event": "request_complete", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "5a1a3d43-69a6-425c-bb59-444f70bc36dc", "endpoint": "/jd_parser", "timestamp": "2025-07-02T15:46:02.952701", "total_time_seconds": 9.658428430557251, "status_code": 200, "message": "Request completed in 9.6584s with status 200"}
{"event": "request_start", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "1413d8b7-3c9c-49b4-9d8f-8baa2710361f", "endpoint": "/intervet_new", "timestamp": "2025-07-02T15:47:16.849916", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "1413d8b7-3c9c-49b4-9d8f-8baa2710361f", "endpoint": "/intervet_new", "timestamp": "2025-07-02T15:47:16.850927", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "1413d8b7-3c9c-49b4-9d8f-8baa2710361f", "endpoint": "/intervet_new", "timestamp": "2025-07-02T15:47:16.867430", "final_score": 4.622727272727273, "message": "Custom metric: final_score=4.622727272727273"}
{"event": "custom_metric", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "1413d8b7-3c9c-49b4-9d8f-8baa2710361f", "endpoint": "/intervet_new", "timestamp": "2025-07-02T15:47:16.867430", "fit_category": "Moderate Match", "message": "Custom metric: fit_category=Moderate Match"}
{"event": "custom_metric", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "1413d8b7-3c9c-49b4-9d8f-8baa2710361f", "endpoint": "/intervet_new", "timestamp": "2025-07-02T15:47:16.867430", "total_credits_used": 10.0, "message": "Custom metric: total_credits_used=10.0"}
{"event": "custom_metric", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "1413d8b7-3c9c-49b4-9d8f-8baa2710361f", "endpoint": "/intervet_new", "timestamp": "2025-07-02T15:47:16.868435", "log_folder": "intervet_new_logs\\intervet_new_20250702_154716_857_1751451436857", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250702_154716_857_1751451436857"}
{"event": "request_complete", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "1413d8b7-3c9c-49b4-9d8f-8baa2710361f", "endpoint": "/intervet_new", "timestamp": "2025-07-02T15:47:16.868435", "total_time_seconds": 0.018518924713134766, "status_code": 200, "message": "Request completed in 0.0185s with status 200"}
{"event": "request_start", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "a806a2a2-ceba-4790-9386-575ac6007be5", "endpoint": "/intervet_new", "timestamp": "2025-07-02T15:49:24.488743", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "a806a2a2-ceba-4790-9386-575ac6007be5", "endpoint": "/intervet_new", "timestamp": "2025-07-02T15:49:24.489743", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "a806a2a2-ceba-4790-9386-575ac6007be5", "endpoint": "/intervet_new", "timestamp": "2025-07-02T15:49:24.503746", "final_score": 5.839160839160839, "message": "Custom metric: final_score=5.839160839160839"}
{"event": "custom_metric", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "a806a2a2-ceba-4790-9386-575ac6007be5", "endpoint": "/intervet_new", "timestamp": "2025-07-02T15:49:24.503746", "fit_category": "Good Match", "message": "Custom metric: fit_category=Good Match"}
{"event": "custom_metric", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "a806a2a2-ceba-4790-9386-575ac6007be5", "endpoint": "/intervet_new", "timestamp": "2025-07-02T15:49:24.503746", "total_credits_used": 13.0, "message": "Custom metric: total_credits_used=13.0"}
{"event": "custom_metric", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "a806a2a2-ceba-4790-9386-575ac6007be5", "endpoint": "/intervet_new", "timestamp": "2025-07-02T15:49:24.503746", "log_folder": "intervet_new_logs\\intervet_new_20250702_154924_495_1751451564495", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250702_154924_495_1751451564495"}
{"event": "request_complete", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "request_id": "a806a2a2-ceba-4790-9386-575ac6007be5", "endpoint": "/intervet_new", "timestamp": "2025-07-02T15:49:24.504746", "total_time_seconds": 0.016002655029296875, "status_code": 200, "message": "Request completed in 0.0160s with status 200"}
{"event": "session_end", "session_id": "9e8fd851-c8cf-4584-a575-274892e49c02", "timestamp": "2025-07-02T15:58:17.225682", "message": "API session ended"}
