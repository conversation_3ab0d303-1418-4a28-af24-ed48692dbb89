
INTERVET_NEW CALCULATION SUMMARY
================================
Timestamp: 20250701_154810_991
Processing Time: 0.001 seconds

FINAL RESULT
============
Total Score: 8.75/10
Fit Category: Excellent Match
Summary: The candidate is a excellent match for this position with a CGPA-style score of 8.8/10. Key strengths: Skills, Experience, Education, Location. Areas for improvement: Certifications.

WEIGHTAGE CONFIGURATION
=======================
Skills: 3.0
Experience: 2.5
Education: 2.0
Certifications: 1.0
Location: 1.0
Reliability: 0.5
Total Credits: 10.0

DETAILED FIELD SCORES
=====================
Skills:
  Raw Score: 9.00/10
  Weight: 3.0
  Weighted Score: 27.00
  Rationale: Matched 8/8 required skills and 2/4 preferred skills. Matched: Python, JavaScript, React, Node.js, SQL, REST APIs, Git, AWS

Experience:
  Raw Score: 10.00/10
  Weight: 2.5
  Weighted Score: 25.00
  Rationale: Excellent match: 4 years vs required 3 years

Education:
  Raw Score: 10.00/10
  Weight: 2.0
  Weighted Score: 20.00
  Rationale: Education requirements met: 'B.Tech Computer Science' matches requirement 'Bachelor's degree in Computer Science or related field' with academic performance of 95.0%

Certifications:
  Raw Score: 2.00/10
  Weight: 1.0
  Weighted Score: 2.00
  Rationale: Found 1 relevant certifications: AWS Certified Developer

Location:
  Raw Score: 10.00/10
  Weight: 1.0
  Weighted Score: 10.00
  Rationale: Current location (san francisco, ca) matches job location (san francisco, ca)

Reliability:
  Raw Score: 7.00/10
  Weight: 0.5
  Weighted Score: 3.50
  Rationale: Good stability: average 2.0 years per company

CALCULATION FORMULA
===================
Final Score = (Sum of Weighted Scores) / (Sum of Weights)
Final Score = (27.00 + 25.00 + 20.00 + 2.00 + 10.00 + 3.50) / 10.0
Final Score = 8.75/10
