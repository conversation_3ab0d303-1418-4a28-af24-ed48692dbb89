#!/usr/bin/env python3
"""
Comprehensive test suite for the enhanced CGPA calculation system.
Tests all scoring functions with real resume and JD data to validate improvements.
"""

import sys
import os
import json
import time
from pathlib import Path

# Add the parent directory to the path so we can import from main
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from main import (
    WeightageConfig, 
    IntervetNewRequest, 
    IntervetNewResponse, 
    FieldScore,
    calculate_candidate_job_fit_new,
    calculate_skills_score_new,
    calculate_experience_score_new,
    calculate_education_score_new,
    calculate_certifications_score_new,
    calculate_location_score_new,
    calculate_reliability_score_new,
    log_intervet_new_calculation
)

def get_sample_resume_data():
    """Get sample resume data for testing"""
    return {
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "phone": "******-0123",
        "location": "San Francisco, CA",
        "skills": [
            "Python", "JavaScript", "React", "Node.js", "SQL", "MongoDB", 
            "REST APIs", "Git", "AWS", "Docker", "HTML", "CSS"
        ],
        "experience": [
            {
                "company": "Tech Corp",
                "position": "Senior Software Engineer",
                "duration": "2020 - Present",
                "location": "San Francisco, CA",
                "description": "Led development of web applications using React and Node.js"
            },
            {
                "company": "StartupXYZ",
                "position": "Full Stack Developer",
                "duration": "2018 - 2020",
                "location": "San Francisco, CA",
                "description": "Developed full-stack applications using Python and JavaScript"
            },
            {
                "company": "WebDev Inc",
                "position": "Junior Developer",
                "duration": "2016 - 2018",
                "location": "San Jose, CA",
                "description": "Built web applications and learned modern development practices"
            }
        ],
        "education": [
            {
                "degree": "Bachelor of Science in Computer Science",
                "institution": "University of California, Berkeley",
                "year": "2016",
                "gpa": "3.7"
            }
        ],
        "certifications": [
            {
                "name": "AWS Certified Developer",
                "issuer": "Amazon Web Services",
                "year": "2021"
            },
            {
                "name": "React Developer Certification",
                "issuer": "Meta",
                "year": "2020"
            }
        ]
    }

def get_sample_jd_data():
    """Get sample job description data for testing"""
    return {
        "title": "Senior Full Stack Developer",
        "company": "Innovation Labs",
        "location": "San Francisco, CA",
        "required_skills": [
            "Python", "JavaScript", "React", "Node.js", "SQL", 
            "REST APIs", "Git", "AWS"
        ],
        "preferred_skills": [
            "Docker", "Kubernetes", "MongoDB", "TypeScript"
        ],
        "required_experience": "3+ years of software development experience",
        "education_requirements": [
            "Bachelor's degree in Computer Science or related field"
        ],
        "description": "We are looking for a senior full stack developer to join our team..."
    }

def test_individual_scoring_functions():
    """Test each enhanced scoring function individually"""
    print("🧪 Testing Enhanced Individual Scoring Functions")
    print("=" * 60)
    
    resume_data = get_sample_resume_data()
    jd_data = get_sample_jd_data()
    
    # Test enhanced skills scoring
    print("\n📊 Testing Enhanced Skills Scoring...")
    skills_score, skills_rationale, skills_details = calculate_skills_score_new(resume_data, jd_data)
    print(f"   Score: {skills_score:.2f}/10")
    print(f"   Rationale: {skills_rationale}")
    print(f"   Calculation Steps: {len(skills_details.get('calculation_steps', []))} steps logged")
    print(f"   Formula: {skills_details.get('scoring_formula', 'N/A')}")
    
    # Test enhanced experience scoring
    print("\n📈 Testing Enhanced Experience Scoring...")
    exp_score, exp_rationale, exp_details = calculate_experience_score_new(resume_data, jd_data)
    print(f"   Score: {exp_score:.2f}/10")
    print(f"   Rationale: {exp_rationale}")
    print(f"   Calculation Steps: {len(exp_details.get('calculation_steps', []))} steps logged")
    print(f"   Experience Breakdown: {len(exp_details.get('experience_breakdown', []))} entries analyzed")
    
    # Test enhanced education scoring (binary)
    print("\n🎓 Testing Enhanced Education Scoring (Binary)...")
    edu_score, edu_rationale, edu_details = calculate_education_score_new(resume_data, jd_data)
    print(f"   Score: {edu_score:.2f}/10")
    print(f"   Rationale: {edu_rationale}")
    print(f"   Match Type: {edu_details.get('match_type', 'N/A')}")
    print(f"   Calculation Steps: {len(edu_details.get('calculation_steps', []))} steps logged")
    
    # Test enhanced certifications scoring
    print("\n🏆 Testing Enhanced Certifications Scoring...")
    cert_score, cert_rationale, cert_details = calculate_certifications_score_new(resume_data, jd_data)
    print(f"   Score: {cert_score:.2f}/10")
    print(f"   Rationale: {cert_rationale}")
    print(f"   Relevant Certifications: {cert_details.get('relevant_count', 0)}")
    print(f"   Analysis: {len(cert_details.get('certification_analysis', []))} certifications analyzed")
    
    # Test enhanced location scoring
    print("\n📍 Testing Enhanced Location Scoring...")
    loc_score, loc_rationale, loc_details = calculate_location_score_new(resume_data, jd_data)
    print(f"   Score: {loc_score:.2f}/10")
    print(f"   Rationale: {loc_rationale}")
    print(f"   Calculation Steps: {len(loc_details.get('calculation_steps', []))} steps logged")
    
    # Test enhanced reliability scoring
    print("\n⚡ Testing Enhanced Reliability Scoring...")
    rel_score, rel_rationale, rel_details = calculate_reliability_score_new(resume_data, jd_data)
    print(f"   Score: {rel_score:.2f}/10")
    print(f"   Rationale: {rel_rationale}")
    print(f"   Average Tenure: {rel_details.get('avg_tenure', 0):.1f} years")
    print(f"   Tenure Breakdown: {len(rel_details.get('tenure_breakdown', []))} jobs analyzed")
    
    return True

def test_enhanced_cgpa_calculation():
    """Test the enhanced CGPA calculation system"""
    print("\n🧪 Testing Enhanced CGPA Calculation System")
    print("=" * 60)
    
    resume_data = get_sample_resume_data()
    jd_data = get_sample_jd_data()
    
    # Test with default weights
    print("\n📈 Test 1: Default Weights")
    print("-" * 40)
    
    start_time = time.time()
    result = calculate_candidate_job_fit_new(resume_data, jd_data)
    processing_time = time.time() - start_time
    
    print(f"✅ Enhanced calculation completed in {processing_time:.3f} seconds")
    print(f"📊 Total Score: {result.total_score:.2f}/10")
    print(f"🎯 Fit Category: {result.fit_category}")
    print(f"⚖️  Total Credits: {result.total_credits_used}")
    print(f"💭 Summary: {result.summary}")
    
    # Verify enhanced logging is working
    print(f"\n🔍 Enhanced Logging Verification:")
    print(f"   Skills calculation steps: {len(result.skills_score.details.get('calculation_steps', []))}")
    print(f"   Experience calculation steps: {len(result.experience_score.details.get('calculation_steps', []))}")
    print(f"   Education calculation steps: {len(result.education_score.details.get('calculation_steps', []))}")
    print(f"   Certifications calculation steps: {len(result.certifications_score.details.get('calculation_steps', []))}")
    print(f"   Location calculation steps: {len(result.location_score.details.get('calculation_steps', []))}")
    print(f"   Reliability calculation steps: {len(result.reliability_score.details.get('calculation_steps', []))}")
    
    # Test with custom weights
    print("\n📈 Test 2: Custom Weights (Skills-Heavy)")
    print("-" * 40)
    
    custom_weights = WeightageConfig(
        skills=5.0,
        experience=3.0,
        education=2.0,
        certifications=1.5,
        location=1.0,
        reliability=0.5
    )
    
    start_time = time.time()
    result2 = calculate_candidate_job_fit_new(resume_data, jd_data, custom_weights)
    processing_time = time.time() - start_time
    
    print(f"✅ Enhanced calculation completed in {processing_time:.3f} seconds")
    print(f"📊 Total Score: {result2.total_score:.2f}/10")
    print(f"🎯 Fit Category: {result2.fit_category}")
    print(f"⚖️  Total Credits: {result2.total_credits_used}")
    
    # Test enhanced logging
    print("\n📝 Testing Enhanced Logging System...")
    log_folder = log_intervet_new_calculation(resume_data, jd_data, custom_weights, result2, "enhanced_test")
    
    if log_folder:
        print(f"✅ Enhanced logs created successfully in: {log_folder}")
        
        # Verify log files exist
        expected_files = [
            "01_input_resume.json",
            "02_input_jd.json", 
            "03_input_weightage.json",
            "04_calculation_breakdown.json",
            "04_step_by_step_calculations.json",
            "05_final_result.json",
            "06_human_readable_summary.txt",
            "07_detailed_calculation_explanation.txt"
        ]
        
        for file_name in expected_files:
            file_path = os.path.join(log_folder, file_name)
            if os.path.exists(file_path):
                print(f"   ✅ {file_name} created successfully")
            else:
                print(f"   ❌ {file_name} missing")
    else:
        print("❌ Enhanced logging failed")
    
    return True

def test_binary_education_scoring():
    """Test the binary education scoring specifically"""
    print("\n🧪 Testing Binary Education Scoring")
    print("=" * 50)
    
    # Test case 1: Exact match
    resume_exact = {
        "education": [
            {"degree": "Bachelor of Science in Computer Science", "institution": "UC Berkeley", "year": "2020"}
        ]
    }
    jd_exact = {
        "education_requirements": ["Bachelor's degree in Computer Science"]
    }
    
    score, rationale, details = calculate_education_score_new(resume_exact, jd_exact)
    print(f"Test 1 - Exact Match: {score:.1f}/10 - {rationale}")
    print(f"   Match Type: {details.get('match_type', 'N/A')}")
    
    # Test case 2: No match
    resume_no_match = {
        "education": [
            {"degree": "Bachelor of Arts in English", "institution": "UC Berkeley", "year": "2020"}
        ]
    }
    
    score, rationale, details = calculate_education_score_new(resume_no_match, jd_exact)
    print(f"Test 2 - No Match: {score:.1f}/10 - {rationale}")
    print(f"   Match Type: {details.get('match_type', 'N/A')}")
    
    # Test case 3: Partial match (benefit of doubt)
    resume_partial = {
        "education": [
            {"degree": "Bachelor of Science in Information Technology", "institution": "UC Berkeley", "year": "2020"}
        ]
    }
    
    score, rationale, details = calculate_education_score_new(resume_partial, jd_exact)
    print(f"Test 3 - Partial Match: {score:.1f}/10 - {rationale}")
    print(f"   Match Type: {details.get('match_type', 'N/A')}")
    
    return True

def main():
    """Run all enhanced CGPA system tests"""
    print("🚀 Enhanced CGPA Calculation System Test Suite")
    print("=" * 70)
    print("Testing all improvements: enhanced logging, binary education scoring,")
    print("detailed calculation steps, and comprehensive transparency.")
    print("=" * 70)
    
    try:
        # Test individual scoring functions
        test_individual_scoring_functions()
        
        # Test enhanced CGPA calculation
        test_enhanced_cgpa_calculation()
        
        # Test binary education scoring
        test_binary_education_scoring()
        
        print("\n" + "=" * 70)
        print("🎉 All Enhanced CGPA System Tests Completed Successfully!")
        print("✅ Enhanced scoring algorithms working correctly")
        print("✅ Detailed calculation logging implemented")
        print("✅ Binary education scoring functional")
        print("✅ Comprehensive transparency achieved")
        print("=" * 70)
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
