{"name": "<PERSON>", "email": "<EMAIL>", "phone": "+1234567890", "location": "San Francisco, CA", "summary": "Experienced software engineer with 5+ years in full-stack development", "education": [{"degree": "B.Tech Computer Science", "institution": "Stanford University", "year": "2018-2022", "gpa": "3.8/4.0"}], "skills": ["Python", "JavaScript", "React", "Node.js", "SQL", "AWS", "<PERSON>er", "Git", "REST APIs", "Machine Learning"], "experience": [{"company_name": "Tech Corp", "role": "Senior Software Engineer", "duration": "2022-Present", "location": "San Francisco, CA", "key_responsibilities": "Developed full-stack applications using React and Node.js, implemented REST APIs, worked with AWS services"}, {"company_name": "StartupXYZ", "role": "Software Developer", "duration": "2020-2022", "location": "San Francisco, CA", "key_responsibilities": "Built web applications using Python and JavaScript, worked with SQL databases"}], "projects": [{"name": "E-commerce Platform", "description": "Built a full-stack e-commerce platform using React, Node.js, and MongoDB", "technologies_used": ["React", "Node.js", "MongoDB", "Express"]}], "certifications": [{"name": "AWS Certified Developer", "issuer": "Amazon Web Services", "date": "2023"}]}