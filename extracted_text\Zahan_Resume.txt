# Extracted Text Debug File
# Source File: <PERSON>ahan_Resume.pdf
# Context: hybrid_resume_parsing
# Extraction Method: pdf_text
# Timestamp: 2025-07-02 10:32:27
# Text Length: 2804 characters
# ================================================

Mohammed Zahan
Tirur, Malappuram, Kerala |<EMAIL> |+918590499109
linkedin.com/in/mohdzahan |github.com/mohdzahan
Experience
Artificial Intelligence Intern , Bay of Bengal Programme Inter Governmental
Organization – RemoteFebruary 2025 – Current
•Developed deep learning models for satellite image analysis, including ship detection, length estimation, and
potential color detection (Faster R-CNN ResNet-50, 0.93 accuracy).
•Worked on ship cluster analysis, extracting latitude, longitude, and ship count.
•Tools and Techniques Used: Faster R-CNN, YOLO, CNN, OpenCV, TensorFlow, PyTorch, Pandas, NumPy
Machine Learning Intern , Zoftcare Solutions – Tirur, IN August 2023 – October 2023
•Developed a book and movie recommendation system using machine learning algorithms and optimised model
performance through data preprocessing and feature engineering.
•Tools and Techniques Used: Numpy, Pandas, Cosine Similarity, Flask
Projects
Toxic Comment Identification System
•Developed and trained a Bidirectional LSTM model for multi-label text classification, achieving 80% validation
accuracy.
•Evaluated and deployed the model using precision, recall, and accuracy metrics for toxic comment identification.
•Tools and Techniques Used: Pandas, Numpy, Tensorflow, LSTM
Audio Based Emotion Recognition System
•Developed a speech emotion recognition system achieving 94% accuracy, leveraging deep learning techniques to
analyze audio waveforms and classify emotional states.
•Implemented an end-to-end machine learning pipeline, encompassing data collection, preprocessing, feature
extraction (MFCC), model training, and evaluation, resulting in effective emotion classification from speech
data.
•Tools and Techniques Used: Python, Librosa, NumPy, Pandas, Scikit-learn, TensorFlow/Keras
Topic Based Analysis of Amazon Customer Feedback
•Conducted detailed analysis of Amazon product reviews to identify recurrent topics and sentiments, enabling a
deeper understanding of customer feedback and pain points.
•Employed topic modeling to extract actionable insights from unstructured textual data, guiding product
improvement and enhancing customer satisfaction strategies based on discovered patterns.
•Tools and Techniques Used: Selenium, BeautifulSoup, Pandas, BERTopic, LDAModel, nltk, spacy
Education
Sri Ramachandra Faculty of Engineering and Technology, Chennai,IN
B.Tech Computer Science Engineering (Artificial Intelligence & Machine Learning)Sept 2021 – Jun 2025
Additional Information
Programming Languages & Tools : Python, SQL, Tableau, HTML, CSS
Soft Skills: Communication, Critical Thinking, Teamwork, Problem Solving, Self Reflection, Analytical Thinking
Areas of Interest: Machine Learning, Artificial Intelligence, Data Analytics
Languages Known: Malayalam, English, Hindi, Tamil
