#!/usr/bin/env python3
"""
Quick test of <PERSON><PERSON><PERSON>'s resume to see detailed logs.
"""

import requests
from pathlib import Path

BASE_URL = "http://localhost:8000"

def quick_test():
    """Quick test with detailed logging."""
    
    print("🧪 Quick Test - <PERSON><PERSON><PERSON>'s Resume")
    print("=" * 50)
    
    resume_path = Path("resumes for testing/Resume-Anurag Pandey.pdf")
    
    if not resume_path.exists():
        print(f"❌ Resume not found: {resume_path}")
        return
    
    print(f"📄 Testing: {resume_path.name}")
    
    try:
        with open(resume_path, 'rb') as f:
            files = {'file': (resume_path.name, f, 'application/pdf')}
            response = requests.post(f"{BASE_URL}/hybrid_resume", files=files, timeout=60)
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            name = result.get('name', 'Unknown')
            print(f"👤 Name extracted: '{name}'")
            
            if name == 'Unknown':
                print("❌ Still getting 'Unknown' - check server logs for details")
            else:
                print("✅ Name extracted successfully!")
                
        else:
            print(f"❌ Request failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == "__main__":
    quick_test()
    print("\n📝 Check the server terminal for detailed logs about JSON parsing")
