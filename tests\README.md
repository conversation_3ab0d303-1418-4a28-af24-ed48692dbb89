# Tests for Gemma API

This directory contains test files for the Gemma API endpoints.

## Test Files

### `test_interfix.py`
Basic tests for the `/interfix` endpoint with standard use cases:
- Complete information extraction
- Partial information extraction  
- Minimal information handling
- Root endpoint verification

### `test_interfix_advanced.py`
Advanced tests for the `/interfix` endpoint with edge cases:
- Different salary formats (LPA, monthly, etc.)
- Multiple salary mentions
- Ambiguous information handling
- Very detailed information extraction
- Empty/minimal summaries
- Error case handling

## Running Tests

Make sure the API server is running first:
```bash
python main.py
```

Then run the tests:
```bash
# Basic tests
python tests/test_interfix.py

# Advanced tests
python tests/test_interfix_advanced.py
```

## Test Results Summary

The `/interfix` endpoint successfully:

✅ **Extracts complete information** when all details are provided
✅ **Handles partial information** gracefully, returning null for missing fields
✅ **Processes different salary formats** (monthly, annual, LPA, etc.)
✅ **Manages ambiguous information** conservatively
✅ **Handles empty summaries** without errors
✅ **Provides proper error responses** for invalid requests
✅ **Listed correctly** in the root endpoint documentation

## Sample API Usage

```python
import requests

# Example request
response = requests.post(
    "http://localhost:8000/interfix",
    json={
        "summary": "The candidate has a 2-month notice period, expects 1 lakh monthly salary, and wants to switch for better growth opportunities. They prefer morning interviews."
    }
)

# Example response
{
    "offer_in_hand": null,
    "notice_period": "2 months", 
    "expected_salary": 100000.0,
    "reason_to_switch": "better growth opportunities",
    "preferred_time_for_interview": "morning",
    "preferred_date_for_interview": null
}
```

## Expected Response Fields

- `offer_in_hand`: Current job offer amount (float or null)
- `notice_period`: Current notice period (string or null)
- `expected_salary`: Expected salary amount (float or null)
- `reason_to_switch`: Reason for job change (string or null)
- `preferred_time_for_interview`: Preferred interview time (string or null)
- `preferred_date_for_interview`: Preferred interview date (string or null)

All fields return `null` when information is not mentioned or cannot be determined from the summary.
