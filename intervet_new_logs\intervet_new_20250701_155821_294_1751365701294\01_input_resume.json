{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "+************", "education": [{"degree": "Bachelor of Technology (Artificial Intelligence)", "institution": "Indian Institute of Technology Madras", "year": "2020 - 2024"}], "skills": {"Processing models": "Mentioned in resume", "Python": "Mentioned in resume", "TensorFlow": "Used as AI Research Intern at Google Research, Bengaluru"}, "experience": [{"company_name": "Google Research, Bengaluru", "role": "AI Research Intern", "duration": "Jan 2024 - Present", "key_responsibilities": "Contributed to state-of-the-art vision-language models for healthcare applications.- Developed optimized training pipelines using TensorFlow and PyTorch."}, {"company_name": "OpenAI, Remote", "role": "Machine Learning Engineer", "duration": "Jun 2023 - Dec 2023", "key_responsibilities": "Designed and deployed NLP models for conversational AI systems.- Collaborated on fine-tuning LLMs for specific business tasks."}], "projects": [], "certifications": ["Python for Beginners - Newton School (2024)"], "domain_of_interest": ["Artificial Intelligence"], "languages_known": ["Python", "C++", "JavaScript"], "social_media": ["https://www.linkedin.com/in/kamalesh-ai/", "https://github.com/kamalesh-ai"], "achievements": ["Designed and deployed NLP models for conversational AI systems.", "Collaborated on fine-tuning LLMs for specific business tasks."], "publications": [], "volunteer_experience": [], "references": [], "summary": "AI Engineer with hands-on experience.", "confidence_score": 0.8, "confidence_details": {}, "processing_time": 11.811439275741577, "extraction_method": "hybrid_regex_llm", "sections_extracted": 8, "regex_confidence": 0.86875}