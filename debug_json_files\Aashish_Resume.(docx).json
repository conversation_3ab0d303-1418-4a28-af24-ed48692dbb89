{
    "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    "email": "<EMAIL>",
    "phone": "+15026539769",
    "education": [
        {
            "degree": "Masters in Artificial Intelligence and Data Science",
            "institution": "Eastern Kentucky University",
            "year": "Dec 2023"
        },
        {
            "degree": "Bachelors in information technology",
            "institution": "University of Bedfordshire",
            "year": "May 2019"
        }
    ],
    "skills": [
        "Java",
        "Spring Boot",
        "Docker",
        "Kubernetes",
        "Angular 6/12",
        "ReactJS 17",
        "HTML5",
        "CSS3",
        "Bootstrap 4/5",
        "Oracle 11g/19c",
        "MongoDB 4.x",
        "RESTful APIs",
        "SOAP web services",
        "JUnit 5",
        "Selenium",
        "Kafka",
        "JMX 1.4",
        "Redis",
        "Cassandra",
        "JavaScript",
        "SQL",
        "PostgreSQL",
        "MySQL",
        "Jackson",
        "Spring Security",
        "OAuth2",
        "JWT",
        "Git",
        "Jenkins 2.303",
        "<PERSON><PERSON>",
        "Artifactory",
        "Eclipse",
        "NetBeans",
        "IntelliJ 14.x/15.x",
        "Visual Studio Code",
        "Webpack",
        "JavaScript",
        "JSON",
        "XML",
        "XSL",
        "HTTP",
        "CI/CD",
        "Agile",
        "Waterfall",
        "TDD",
        "Design Patterns",
        "Microservices",
        "Data Streaming",
        "Database Management",
        "API Development",
        "Web Services",
        "System Design",
        "Distributed Systems",
        "Cloud Computing"
    ],
    "experience": [
        {
            "company_name": "First Republic Bank",
            "role": "Senior Software Developer",
            "duration": "Dec 2022- Present",
            "description": "Developed and maintained backend services using Java and Spring 3.x for processing financial transactions and customer data."
        },
        {
            "company_name": "Fuse machines, Ave",
            "role": "Software Developer",
            "duration": "July 2017-Feb 2019",
            "description": "Developed and maintained large-scale monolithic applications using Java and Spring. Built interactive applications using HTML5, CSS3, JavaScript, jQuery, AJAX, JSON, ReactJS, Backbone.js, and Bootstrap."
        },
        {
            "company_name": "-",
            "role": "Software Developer",
            "duration": "June 2015 - June 2017",
            "description": "Developed and maintained web applications using JavaScript, HTML, CSS, and AJAX. Implemented RESTful APIs and data validation.  Collaborated with frontend and backend teams to deliver high-quality web applications."
        }
    ],
    "education": [
        {
            "degree": "Bachelors in information technology",
            "institution": "University of Bedfordshire",
            "year": "May 2019"
        }