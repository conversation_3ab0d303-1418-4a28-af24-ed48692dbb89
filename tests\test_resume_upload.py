#!/usr/bin/env python3
"""
Test script to upload a resume and test the new debug filename system
"""

import requests
import os

def test_resume_upload():
    """Test resume upload and check for debug files"""
    
    print("🧪 Testing Resume Upload and Debug Filename System")
    print("=" * 60)
    
    # Test file
    test_file = "test_resume_mehak_jain.txt"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file {test_file} not found")
        return
    
    print(f"📄 Testing with file: {test_file}")
    
    try:
        # Upload the resume
        with open(test_file, 'rb') as f:
            files = {'file': (test_file, f, 'text/plain')}
            
            print("🔄 Uploading resume...")
            response = requests.post(
                "http://localhost:8000/resume",
                files=files,
                timeout=120
            )
            
            print(f"📡 Response Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Resume processed successfully!")
                
                # Check if there were any errors that might have created debug files
                if "error" in result:
                    print(f"⚠️  Processing had errors: {result['error']}")
                else:
                    print(f"👤 Extracted name: {result.get('name', 'Unknown')}")
                    print(f"📧 Extracted email: {result.get('email', 'None')}")
                    print(f"🛠️  Extracted skills count: {len(result.get('skills', []))}")
            else:
                print(f"❌ Upload failed: {response.status_code}")
                print(f"Response: {response.text[:500]}")
                
    except Exception as e:
        print(f"❌ Error during upload: {e}")
    
    # Check for debug files
    print("\n📁 Checking for debug files...")
    try:
        response = requests.get("http://localhost:8000/debug/json-files")
        if response.status_code == 200:
            data = response.json()
            print(f"Found {data['total_files']} debug files in {data.get('debug_folder', 'current directory')}")
            
            if data['debug_files']:
                print("\n🔍 Debug files found:")
                for file_info in data['debug_files']:
                    print(f"  📄 {file_info['filename']}")
                    print(f"     Size: {file_info['size']} bytes")
                    print(f"     Context: {file_info['context']}")
                    print(f"     Created: {file_info['created']}")
                    print()
                    
                    # Test the expected filename format
                    if "mehak" in file_info['filename'].lower():
                        print(f"  ✅ Found debug file with expected name pattern!")
                        
                        # Test accessing the file
                        file_response = requests.get(f"http://localhost:8000/debug/json-files/{file_info['filename']}")
                        if file_response.status_code == 200:
                            file_data = file_response.json()
                            print(f"  📖 File accessible: {file_data['length']} characters")
                            if isinstance(file_data['error_analysis'], dict):
                                print(f"  🔍 JSON Error: {file_data['error_analysis']['error']}")
                        else:
                            print(f"  ❌ Could not access file: {file_response.status_code}")
            else:
                print("📭 No debug files found (this means JSON parsing was successful!)")
        else:
            print(f"❌ Could not check debug files: {response.status_code}")
    except Exception as e:
        print(f"❌ Error checking debug files: {e}")

def test_multiple_uploads():
    """Test multiple uploads of the same file to test collision handling"""
    
    print("\n🔄 Testing filename collision handling...")
    print("=" * 60)
    
    test_file = "test_resume_mehak_jain.txt"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file {test_file} not found")
        return
    
    # First, let's create a scenario that might cause JSON errors
    # We'll modify the LLM timeout to be very short to potentially cause issues
    
    print("📄 Processing the same file multiple times...")
    
    for i in range(3):
        print(f"\n🔄 Upload attempt {i+1}:")
        try:
            with open(test_file, 'rb') as f:
                files = {'file': (test_file, f, 'text/plain')}
                
                response = requests.post(
                    "http://localhost:8000/resume",
                    files=files,
                    timeout=120
                )
                
                print(f"  Status: {response.status_code}")
                if response.status_code == 200:
                    result = response.json()
                    if "error" in result:
                        print(f"  ⚠️  Had errors (might create debug file)")
                    else:
                        print(f"  ✅ Success")
                        
        except Exception as e:
            print(f"  ❌ Error: {e}")
    
    # Check final debug files
    print("\n📁 Final debug file check:")
    try:
        response = requests.get("http://localhost:8000/debug/json-files")
        if response.status_code == 200:
            data = response.json()
            print(f"Total debug files: {data['total_files']}")
            
            # Look for files with incremental numbers
            mehak_files = [f for f in data['debug_files'] if 'mehak' in f['filename'].lower()]
            if mehak_files:
                print(f"Found {len(mehak_files)} files related to mehak_jain:")
                for file_info in mehak_files:
                    print(f"  - {file_info['filename']}")
            else:
                print("No mehak-related debug files (JSON parsing was successful)")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🚀 Debug Filename System Test")
    print("Testing meaningful debug file naming with actual resume processing...")
    
    # Test 1: Single upload
    test_resume_upload()
    
    # Test 2: Multiple uploads for collision testing
    test_multiple_uploads()
    
    print("\n" + "=" * 60)
    print("🎉 Testing completed!")
    print("\nExpected behavior:")
    print("✅ If JSON parsing succeeds: No debug files created")
    print("✅ If JSON parsing fails: Debug file named like 'test_resume_mehak_jain(txt).json'")
    print("✅ Multiple failures: 'test_resume_mehak_jain(txt)1.json', 'test_resume_mehak_jain(txt)2.json', etc.")
    print("✅ Files organized in 'debug_json_files' folder")
