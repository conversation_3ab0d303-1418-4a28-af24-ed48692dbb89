"""
Test script for literal section extraction.

This script tests the improved literal extraction that copies content
exactly as it appears under specific section headings.
"""

import requests
import json
import os
import time
from pathlib import Path

# API base URL
BASE_URL = "http://localhost:8000"

def create_test_resume_text():
    """Create a test resume with clear section boundaries."""
    return """
<PERSON>
Software Engineer
Email: <EMAIL>
Phone: (*************

SUMMARY
Experienced software engineer with 5+ years in full-stack development.
Passionate about creating scalable solutions and leading teams.

EDUCATION
Bachelor of Science in Computer Science
University of Technology, 2016-2020
GPA: 3.8/4.0

Master of Science in Software Engineering  
Tech University, 2020-2022
Thesis: "Scalable Microservices Architecture"

EXPERIENCE
Senior Software Engineer
TechCorp Inc., San Francisco, CA
January 2022 - Present
• Led development of microservices architecture
• Mentored junior developers
• Implemented CI/CD pipelines

Software Engineer
StartupXYZ, Palo Alto, CA
June 2020 - December 2021
• Developed full-stack web applications
• Built RESTful APIs
• Optimized database performance

SKILLS
Programming Languages: Python, JavaScript, Java
Frameworks: React, Node.js, Django, Flask
Databases: PostgreSQL, MySQL, MongoDB
Cloud: AWS, Docker, Kubernetes
Tools: Git, Jenkins, JIRA

PROJECTS
E-commerce Platform
• Built full-stack platform with React and Node.js
• Implemented payment processing with Stripe
• Deployed on AWS with auto-scaling

Movie Recommendation System
• Developed ML-based recommendation engine
• Used collaborative filtering algorithms
• Achieved 85% accuracy in predictions

CERTIFICATIONS
AWS Certified Solutions Architect - Associate (2023)
Google Cloud Professional Data Engineer (2022)
Certified Kubernetes Administrator (2021)

ACHIEVEMENTS
Winner of TechCorp Hackathon 2023
Published research paper on "Scalable ML Systems"
Speaker at PyData Conference 2022
Dean's List for 4 consecutive semesters

LANGUAGES
English: Native
Spanish: Conversational
French: Basic
"""

def test_literal_extraction_api():
    """Test the literal extraction using API endpoints."""
    print("🧪 Testing Literal Section Extraction via API")
    print("=" * 60)
    
    # Find test resume files
    resume_test_files = []
    test_dirs = ["resumes for testing", "resumes", "test_files"]
    
    for test_dir in test_dirs:
        if Path(test_dir).exists():
            resume_test_files.extend(list(Path(test_dir).glob("*.pdf")))
            resume_test_files.extend(list(Path(test_dir).glob("*.docx")))
    
    if not resume_test_files:
        print("❌ No test resume files found!")
        print("Please add some resume files to test with.")
        return
    
    # Test with the first available resume
    test_file = resume_test_files[0]
    print(f"📄 Testing with file: {test_file.name}")
    print()
    
    # Test multiple calls method
    print("1. Testing Multiple Calls Method (/section)")
    print("-" * 50)
    
    try:
        with open(test_file, 'rb') as f:
            files = {'file': (test_file.name, f, 'application/pdf' if test_file.suffix == '.pdf' else 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')}
            response = requests.post(f"{BASE_URL}/section", files=files, timeout=300)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Multiple calls method successful!")
            print(f"📊 Overall confidence: {result['overall_confidence']:.2f}")
            
            # Show section extraction results
            print("\n📋 Section Extraction Results:")
            for section, content in result['sections_extracted'].items():
                confidence = result['confidence_scores'].get(section, 0.0)
                if content and content.strip() and content != "NOT_FOUND":
                    preview = content[:100] + "..." if len(content) > 100 else content
                    print(f"   ✅ {section.capitalize()}: {confidence:.2f} - {preview}")
                else:
                    print(f"   ❌ {section.capitalize()}: {confidence:.2f} - NOT_FOUND")
        else:
            print(f"❌ Multiple calls method failed: {response.status_code}")
    
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n" + "=" * 60)
    
    # Test single call method
    print("2. Testing Single Call Method (/section2)")
    print("-" * 50)
    
    try:
        with open(test_file, 'rb') as f:
            files = {'file': (test_file.name, f, 'application/pdf' if test_file.suffix == '.pdf' else 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')}
            response = requests.post(f"{BASE_URL}/section2", files=files, timeout=300)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Single call method successful!")
            print(f"📊 Overall confidence: {result['overall_confidence']:.2f}")
            
            # Show section extraction results
            print("\n📋 Section Extraction Results:")
            for section, content in result['sections_extracted'].items():
                confidence = result['confidence_scores'].get(section, 0.0)
                if content and content.strip() and content != "NOT_FOUND":
                    preview = content[:100] + "..." if len(content) > 100 else content
                    print(f"   ✅ {section.capitalize()}: {confidence:.2f} - {preview}")
                else:
                    print(f"   ❌ {section.capitalize()}: {confidence:.2f} - NOT_FOUND")
        else:
            print(f"❌ Single call method failed: {response.status_code}")
    
    except Exception as e:
        print(f"❌ Error: {e}")

def analyze_extraction_quality():
    """Analyze the quality of literal extraction."""
    print("\n📊 Extraction Quality Analysis")
    print("=" * 60)
    
    # Check recent extraction files
    sections_dir = Path("resume sections extracted")
    if sections_dir.exists():
        section_files = list(sections_dir.glob("*.txt"))
        if section_files:
            # Get the most recent file
            latest_file = max(section_files, key=lambda x: x.stat().st_mtime)
            print(f"📄 Analyzing: {latest_file.name}")
            
            try:
                with open(latest_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Look for section markers
                sections = {}
                current_section = None
                current_content = []
                
                lines = content.split('\n')
                for line in lines:
                    line = line.strip()
                    
                    # Check for section markers
                    if line.startswith('[') and line.endswith(']'):
                        # Save previous section
                        if current_section and current_content:
                            sections[current_section] = '\n'.join(current_content).strip()
                        
                        # Start new section
                        current_section = line[1:-1].lower()
                        current_content = []
                    elif line == "----------------------------------------":
                        continue  # Skip separator lines
                    elif line and current_section:
                        current_content.append(line)
                
                # Don't forget the last section
                if current_section and current_content:
                    sections[current_section] = '\n'.join(current_content).strip()
                
                # Analyze each section
                print("\n🔍 Section Analysis:")
                for section_name, section_content in sections.items():
                    if section_content and section_content != "NOT_FOUND":
                        lines_count = len(section_content.split('\n'))
                        char_count = len(section_content)
                        print(f"   ✅ {section_name.upper()}: {lines_count} lines, {char_count} chars")
                        
                        # Show first line as preview
                        first_line = section_content.split('\n')[0]
                        if len(first_line) > 80:
                            first_line = first_line[:80] + "..."
                        print(f"      Preview: {first_line}")
                    else:
                        print(f"   ❌ {section_name.upper()}: NOT_FOUND")
                
            except Exception as e:
                print(f"❌ Error analyzing file: {e}")
        else:
            print("❌ No extraction files found")
    else:
        print("❌ No 'resume sections extracted' directory found")

def check_conversation_logs():
    """Check conversation logs for literal extraction."""
    print("\n📁 Conversation Logs Analysis")
    print("=" * 60)
    
    conv_dir = Path("conv")
    if conv_dir.exists():
        conv_folders = [f for f in conv_dir.iterdir() if f.is_dir()]
        if conv_folders:
            # Get the most recent conversation
            latest_conv = max(conv_folders, key=lambda x: x.stat().st_mtime)
            print(f"📂 Latest conversation: {latest_conv.name}")
            
            # Check final results
            final_results = latest_conv / "final_results_summary.txt"
            if final_results.exists():
                print(f"\n📊 Final Results Summary:")
                try:
                    with open(final_results, 'r', encoding='utf-8') as f:
                        content = f.read()
                        # Show key metrics
                        lines = content.split('\n')
                        for line in lines:
                            if any(keyword in line for keyword in ['Processing Time:', 'Total LLM Calls:', 'Overall Confidence:', 'Sections Found:']):
                                print(f"   {line}")
                except Exception as e:
                    print(f"❌ Error reading results: {e}")
        else:
            print("❌ No conversation folders found")
    else:
        print("❌ No 'conv' directory found")

def provide_improvement_tips():
    """Provide tips for better literal extraction."""
    print("\n💡 Tips for Better Literal Extraction")
    print("=" * 60)
    
    print("✅ What the improved extraction does:")
    print("   - Looks for specific section headings in the resume")
    print("   - Extracts only content directly under each heading")
    print("   - Avoids mixing content from different sections")
    print("   - Maintains original formatting and structure")
    print("   - Special handling for skills (compiles from entire resume)")
    
    print("\n🎯 Expected behavior:")
    print("   - Projects under 'PROJECTS' section only")
    print("   - Achievements under 'ACHIEVEMENTS' section only")
    print("   - Experience under 'EXPERIENCE' section only")
    print("   - No cross-contamination between sections")
    
    print("\n📝 Testing recommendations:")
    print("   - Use resumes with clear section headings")
    print("   - Check that content stays within section boundaries")
    print("   - Verify skills compilation includes all relevant skills")
    print("   - Compare multiple vs single call methods")

if __name__ == "__main__":
    print("🚀 Literal Section Extraction Testing")
    print("This tests the improved extraction that copies content exactly as it appears under section headings.")
    print()
    
    # Wait for user confirmation
    input("Press Enter to start testing...")
    
    # Run API tests
    test_literal_extraction_api()
    
    # Analyze extraction quality
    analyze_extraction_quality()
    
    # Check conversation logs
    check_conversation_logs()
    
    # Provide tips
    provide_improvement_tips()
    
    print("\n✨ Testing completed!")
    print("The extraction should now be more literal and section-specific.")
    print("Check the results to ensure content stays within proper section boundaries.")
