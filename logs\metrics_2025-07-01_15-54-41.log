{"event": "session_start", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "timestamp": "2025-07-01T15:54:41.200197", "message": "New API session started"}
{"event": "request_start", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "c33001b6-11fb-4e19-b751-626aef095b55", "endpoint": "/", "timestamp": "2025-07-01T15:54:42.676395", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "c33001b6-11fb-4e19-b751-626aef095b55", "endpoint": "/", "timestamp": "2025-07-01T15:54:42.677394", "total_time_seconds": 0.0009992122650146484, "status_code": 200, "message": "Request completed in 0.0010s with status 200"}
{"event": "request_start", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "d6a46fbb-8cc9-496d-8c40-3303ca0b44db", "endpoint": "/favicon.ico", "timestamp": "2025-07-01T15:54:42.776529", "message": "Request started for endpoint: /favicon.ico"}
{"event": "request_complete", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "d6a46fbb-8cc9-496d-8c40-3303ca0b44db", "endpoint": "/favicon.ico", "timestamp": "2025-07-01T15:54:42.777527", "total_time_seconds": 0.000997781753540039, "status_code": 404, "message": "Request completed in 0.0010s with status 404"}
{"event": "request_start", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "0090d1ba-b7e2-4700-b56c-94c7b0e1a377", "endpoint": "/docs", "timestamp": "2025-07-01T15:54:46.360631", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "0090d1ba-b7e2-4700-b56c-94c7b0e1a377", "endpoint": "/docs", "timestamp": "2025-07-01T15:54:46.360631", "total_time_seconds": 0.0, "status_code": 200, "message": "Request completed in 0.0000s with status 200"}
{"event": "request_start", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "0899f9f8-a285-4d56-996c-98446e4a85f3", "endpoint": "/openapi.json", "timestamp": "2025-07-01T15:54:46.505131", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "0899f9f8-a285-4d56-996c-98446e4a85f3", "endpoint": "/openapi.json", "timestamp": "2025-07-01T15:54:46.521641", "total_time_seconds": 0.016510009765625, "status_code": 200, "message": "Request completed in 0.0165s with status 200"}
{"event": "request_start", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "86b00800-97a5-4275-97dd-0aad4bf3e2df", "endpoint": "/hybrid_resume", "timestamp": "2025-07-01T15:55:08.026755", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "86b00800-97a5-4275-97dd-0aad4bf3e2df", "endpoint": "/hybrid_resume", "timestamp": "2025-07-01T15:55:08.075757", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "86b00800-97a5-4275-97dd-0aad4bf3e2df", "endpoint": "/hybrid_resume", "timestamp": "2025-07-01T15:55:08.075757", "file_size_bytes": 39010, "message": "Custom metric: file_size_bytes=39010"}
{"event": "custom_metric", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "86b00800-97a5-4275-97dd-0aad4bf3e2df", "endpoint": "/hybrid_resume", "timestamp": "2025-07-01T15:55:08.075757", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "86b00800-97a5-4275-97dd-0aad4bf3e2df", "endpoint": "/hybrid_resume", "timestamp": "2025-07-01T15:55:08.075757", "extracted_text_length": 1436, "message": "Custom metric: extracted_text_length=1436"}
{"event": "custom_metric", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "86b00800-97a5-4275-97dd-0aad4bf3e2df", "endpoint": "/hybrid_resume", "timestamp": "2025-07-01T15:55:08.075757", "file_processing_time": 0.04497861862182617, "message": "Custom metric: file_processing_time=0.04497861862182617"}
{"event": "request_complete", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "86b00800-97a5-4275-97dd-0aad4bf3e2df", "endpoint": "/hybrid_resume", "timestamp": "2025-07-01T15:55:19.840271", "total_time_seconds": 11.81351613998413, "status_code": 200, "message": "Request completed in 11.8135s with status 200"}
{"event": "request_start", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "fcfd9f07-894d-4643-bcb4-abad31bf1f6a", "endpoint": "/jd_parser", "timestamp": "2025-07-01T15:55:33.585733", "message": "Request started for endpoint: /jd_parser"}
{"event": "request_complete", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "fcfd9f07-894d-4643-bcb4-abad31bf1f6a", "endpoint": "/jd_parser", "timestamp": "2025-07-01T15:55:43.262177", "total_time_seconds": 9.676443815231323, "status_code": 200, "message": "Request completed in 9.6764s with status 200"}
{"event": "request_start", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "7a393445-1968-4923-b6fb-e9e3a0050d8e", "endpoint": "/intervet_new", "timestamp": "2025-07-01T15:56:36.747248", "message": "Request started for endpoint: /intervet_new"}
{"event": "request_complete", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "7a393445-1968-4923-b6fb-e9e3a0050d8e", "endpoint": "/intervet_new", "timestamp": "2025-07-01T15:56:36.748245", "total_time_seconds": 0.0009975433349609375, "status_code": 422, "message": "Request completed in 0.0010s with status 422"}
{"event": "request_start", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "aff94228-3bd4-4955-885e-3ce692c36f7c", "endpoint": "/intervet_new", "timestamp": "2025-07-01T15:58:21.290861", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "aff94228-3bd4-4955-885e-3ce692c36f7c", "endpoint": "/intervet_new", "timestamp": "2025-07-01T15:58:21.292861", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "aff94228-3bd4-4955-885e-3ce692c36f7c", "endpoint": "/intervet_new", "timestamp": "2025-07-01T15:58:21.300863", "final_score": 3.35, "message": "Custom metric: final_score=3.35"}
{"event": "custom_metric", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "aff94228-3bd4-4955-885e-3ce692c36f7c", "endpoint": "/intervet_new", "timestamp": "2025-07-01T15:58:21.300863", "fit_category": "Weak Match", "message": "Custom metric: fit_category=Weak Match"}
{"event": "custom_metric", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "aff94228-3bd4-4955-885e-3ce692c36f7c", "endpoint": "/intervet_new", "timestamp": "2025-07-01T15:58:21.300863", "total_credits_used": 10.0, "message": "Custom metric: total_credits_used=10.0"}
{"event": "custom_metric", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "aff94228-3bd4-4955-885e-3ce692c36f7c", "endpoint": "/intervet_new", "timestamp": "2025-07-01T15:58:21.300863", "log_folder": "intervet_new_logs\\intervet_new_20250701_155821_294_1751365701294", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250701_155821_294_1751365701294"}
{"event": "request_complete", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "aff94228-3bd4-4955-885e-3ce692c36f7c", "endpoint": "/intervet_new", "timestamp": "2025-07-01T15:58:21.301864", "total_time_seconds": 0.011002540588378906, "status_code": 200, "message": "Request completed in 0.0110s with status 200"}
{"event": "request_start", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "57950d77-cde2-4607-be5f-4fd9e7fe1c56", "endpoint": "/hybrid_resume", "timestamp": "2025-07-01T15:59:33.277477", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "57950d77-cde2-4607-be5f-4fd9e7fe1c56", "endpoint": "/hybrid_resume", "timestamp": "2025-07-01T15:59:33.303993", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "57950d77-cde2-4607-be5f-4fd9e7fe1c56", "endpoint": "/hybrid_resume", "timestamp": "2025-07-01T15:59:33.303993", "file_size_bytes": 60820, "message": "Custom metric: file_size_bytes=60820"}
{"event": "custom_metric", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "57950d77-cde2-4607-be5f-4fd9e7fe1c56", "endpoint": "/hybrid_resume", "timestamp": "2025-07-01T15:59:33.303993", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "57950d77-cde2-4607-be5f-4fd9e7fe1c56", "endpoint": "/hybrid_resume", "timestamp": "2025-07-01T15:59:33.303993", "extracted_text_length": 2804, "message": "Custom metric: extracted_text_length=2804"}
{"event": "custom_metric", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "57950d77-cde2-4607-be5f-4fd9e7fe1c56", "endpoint": "/hybrid_resume", "timestamp": "2025-07-01T15:59:33.303993", "file_processing_time": 0.022999286651611328, "message": "Custom metric: file_processing_time=0.022999286651611328"}
{"event": "request_complete", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "57950d77-cde2-4607-be5f-4fd9e7fe1c56", "endpoint": "/hybrid_resume", "timestamp": "2025-07-01T16:00:05.301869", "total_time_seconds": 32.0243923664093, "status_code": 200, "message": "Request completed in 32.0244s with status 200"}
{"event": "request_start", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "eb62962a-567e-4d15-94d5-0f445b412271", "endpoint": "/intervet_new", "timestamp": "2025-07-01T16:00:43.686819", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "eb62962a-567e-4d15-94d5-0f445b412271", "endpoint": "/intervet_new", "timestamp": "2025-07-01T16:00:43.687813", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "eb62962a-567e-4d15-94d5-0f445b412271", "endpoint": "/intervet_new", "timestamp": "2025-07-01T16:00:43.695813", "final_score": 4.63, "message": "Custom metric: final_score=4.63"}
{"event": "custom_metric", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "eb62962a-567e-4d15-94d5-0f445b412271", "endpoint": "/intervet_new", "timestamp": "2025-07-01T16:00:43.695813", "fit_category": "Moderate Match", "message": "Custom metric: fit_category=Moderate Match"}
{"event": "custom_metric", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "eb62962a-567e-4d15-94d5-0f445b412271", "endpoint": "/intervet_new", "timestamp": "2025-07-01T16:00:43.695813", "total_credits_used": 10.0, "message": "Custom metric: total_credits_used=10.0"}
{"event": "custom_metric", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "eb62962a-567e-4d15-94d5-0f445b412271", "endpoint": "/intervet_new", "timestamp": "2025-07-01T16:00:43.695813", "log_folder": "intervet_new_logs\\intervet_new_20250701_160043_690_1751365843690", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250701_160043_690_1751365843690"}
{"event": "request_complete", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "eb62962a-567e-4d15-94d5-0f445b412271", "endpoint": "/intervet_new", "timestamp": "2025-07-01T16:00:43.696814", "total_time_seconds": 0.009995460510253906, "status_code": 200, "message": "Request completed in 0.0100s with status 200"}
{"event": "request_start", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "4ee4c58b-c427-4b19-98ab-06526b129dca", "endpoint": "/intervet_new", "timestamp": "2025-07-01T16:02:30.865541", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "4ee4c58b-c427-4b19-98ab-06526b129dca", "endpoint": "/intervet_new", "timestamp": "2025-07-01T16:02:30.866543", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "4ee4c58b-c427-4b19-98ab-06526b129dca", "endpoint": "/intervet_new", "timestamp": "2025-07-01T16:02:30.875111", "final_score": 5.28, "message": "Custom metric: final_score=5.28"}
{"event": "custom_metric", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "4ee4c58b-c427-4b19-98ab-06526b129dca", "endpoint": "/intervet_new", "timestamp": "2025-07-01T16:02:30.875111", "fit_category": "Moderate Match", "message": "Custom metric: fit_category=Moderate Match"}
{"event": "custom_metric", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "4ee4c58b-c427-4b19-98ab-06526b129dca", "endpoint": "/intervet_new", "timestamp": "2025-07-01T16:02:30.875111", "total_credits_used": 12.5, "message": "Custom metric: total_credits_used=12.5"}
{"event": "custom_metric", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "4ee4c58b-c427-4b19-98ab-06526b129dca", "endpoint": "/intervet_new", "timestamp": "2025-07-01T16:02:30.876112", "log_folder": "intervet_new_logs\\intervet_new_20250701_160230_869_1751365950869", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250701_160230_869_1751365950869"}
{"event": "request_complete", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "request_id": "4ee4c58b-c427-4b19-98ab-06526b129dca", "endpoint": "/intervet_new", "timestamp": "2025-07-01T16:02:30.876112", "total_time_seconds": 0.010571002960205078, "status_code": 200, "message": "Request completed in 0.0106s with status 200"}
{"event": "session_end", "session_id": "fefd6a1a-2d92-41dc-8acc-f8ac9194c897", "timestamp": "2025-07-01T16:03:34.761827", "message": "API session ended"}
