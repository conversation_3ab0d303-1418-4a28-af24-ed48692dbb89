
INTERVET_NEW CALCULATION SUMMARY
================================
Timestamp: 20250701_160230_869
Processing Time: 0.003 seconds

FINAL RESULT
============
Total Score: 5.28/10
Fit Category: Moderate Match
Summary: The candidate is a moderate match for this position with a CGPA-style score of 5.3/10. Key strengths: Education. Areas for improvement: Skills, Certifications.

WEIGHTAGE CONFIGURATION
=======================
Skills: 5.0
Experience: 2.0
Education: 5.0
Certifications: 0.5
Location: 0.0
Reliability: 0.0
Total Credits: 12.5

DETAILED FIELD SCORES
=====================
Skills:
  Raw Score: 3.20/10
  Weight: 5.0
  Weighted Score: 16.00
  Rationale: Matched 2/5 required skills and 0/1 preferred skills. Matched: HTML, CSS. Missing: Javascript, Media queries, SDLC

Experience:
  Raw Score: 5.00/10
  Weight: 2.0
  Weighted Score: 10.00
  Rationale: Could not determine candidate's experience to compare with required 1 years

Education:
  Raw Score: 8.00/10
  Weight: 5.0
  Weighted Score: 40.00
  Rationale: Education requirements met: 'B.Tech Computer Science Engineering (Artificial Intelligence & Machine Learning)' matches requirement 'Bachelor’s degree in IT, Computer Science, Software Engineering, or a related field.'

Certifications:
  Raw Score: 0.00/10
  Weight: 0.5
  Weighted Score: 0.00
  Rationale: No certifications found in resume

Location:
  Raw Score: 5.00/10
  Weight: 0.0
  Weighted Score: 0.00
  Rationale: Location information not available for comparison

Reliability:
  Raw Score: 5.00/10
  Weight: 0.0
  Weighted Score: 0.00
  Rationale: Could not calculate job stability due to missing experience data

CALCULATION FORMULA
===================
Final Score = (Sum of Weighted Scores) / (Sum of Weights)
Final Score = (16.00 + 10.00 + 40.00 + 0.00 + 0.00 + 0.00) / 12.5
Final Score = 5.28/10
