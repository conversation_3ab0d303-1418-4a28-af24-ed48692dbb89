#!/usr/bin/env python3
"""
Test script for the new debug filename system
"""

import requests
import json
import os
import time

# API endpoint
BASE_URL = "http://localhost:8000"

def test_debug_filename_system():
    """Test the new debug filename system with meaningful names"""
    
    print("🧪 Testing Debug Filename System")
    print("=" * 50)
    
    # Test 1: Check current debug files
    print("\n📁 Current debug files:")
    try:
        response = requests.get(f"{BASE_URL}/debug/json-files")
        if response.status_code == 200:
            data = response.json()
            print(f"Found {data['total_files']} debug files in {data.get('debug_folder', 'current directory')}")
            
            for file_info in data['debug_files'][:5]:  # Show first 5
                print(f"  - {file_info['filename']} ({file_info['size']} bytes, {file_info['context']})")
        else:
            print(f"❌ Error getting debug files: {response.status_code}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 2: Process a resume to generate debug files (if there are resumes available)
    print("\n📄 Testing resume processing to generate debug files...")
    
    # Check if there are any resume files in the resumes folder
    resume_folder = "resumes"
    if os.path.exists(resume_folder):
        resume_files = [f for f in os.listdir(resume_folder) if f.lower().endswith(('.pdf', '.docx', '.doc'))]
        
        if resume_files:
            test_file = resume_files[0]
            print(f"📋 Testing with resume: {test_file}")
            
            try:
                with open(os.path.join(resume_folder, test_file), 'rb') as f:
                    files = {'file': (test_file, f, 'application/pdf' if test_file.endswith('.pdf') else 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')}
                    
                    print(f"🔄 Processing {test_file}...")
                    response = requests.post(f"{BASE_URL}/resume", files=files, timeout=120)
                    
                    if response.status_code == 200:
                        result = response.json()
                        if "error" in result:
                            print(f"⚠️  Resume processed with errors (this might generate debug files)")
                        else:
                            print(f"✅ Resume processed successfully")
                    else:
                        print(f"❌ Resume processing failed: {response.status_code}")
                        
            except Exception as e:
                print(f"❌ Error processing resume: {e}")
        else:
            print("📭 No resume files found in resumes folder")
    else:
        print("📭 Resumes folder not found")
    
    # Test 3: Check debug files after processing
    print("\n📁 Debug files after processing:")
    try:
        response = requests.get(f"{BASE_URL}/debug/json-files")
        if response.status_code == 200:
            data = response.json()
            print(f"Found {data['total_files']} debug files")
            
            # Show the most recent files
            for file_info in data['debug_files'][:3]:
                print(f"  - {file_info['filename']}")
                print(f"    Size: {file_info['size']} bytes")
                print(f"    Context: {file_info['context']}")
                print(f"    Created: {file_info['created']}")
                print()
                
                # Test getting the file content
                file_response = requests.get(f"{BASE_URL}/debug/json-files/{file_info['filename']}")
                if file_response.status_code == 200:
                    file_data = file_response.json()
                    print(f"    ✅ File content accessible ({file_data['length']} chars)")
                    if isinstance(file_data['error_analysis'], dict):
                        print(f"    🔍 JSON Error: {file_data['error_analysis']['error']}")
                        print(f"    📍 Location: Line {file_data['error_analysis']['line']}, Column {file_data['error_analysis']['column']}")
                else:
                    print(f"    ❌ Could not access file content: {file_response.status_code}")
                print("-" * 30)
        else:
            print(f"❌ Error getting debug files: {response.status_code}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 4: Test filename collision handling
    print("\n🔄 Testing filename collision handling...")
    print("(This would require processing the same file multiple times with JSON errors)")
    
    # Test 5: Test cleanup
    print("\n🧹 Testing debug file cleanup...")
    try:
        response = requests.delete(f"{BASE_URL}/debug/json-files")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Cleaned up {data['total_deleted']} debug files")
            print(f"📁 From folder: {data.get('debug_folder', 'current directory')}")
        else:
            print(f"❌ Cleanup failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Cleanup error: {e}")

def test_filename_generation():
    """Test the filename generation logic"""
    print("\n🏷️  Testing filename generation logic...")
    
    # Test cases for different file types
    test_cases = [
        ("mehak_jain_resume.pdf", "mehakjain(pdf).json"),
        ("john-doe-cv.docx", "johndoecv(docx).json"),
        ("resume_2024.doc", "resume2024(doc).json"),
        ("my resume (final).pdf", "myresumefinal(pdf).json"),
        ("<EMAIL>", "candidateemailcom(pdf).json"),
    ]
    
    print("Expected filename transformations:")
    for original, expected in test_cases:
        print(f"  {original} → {expected}")
    
    print("\nCollision handling:")
    print("  mehakjain(pdf).json → mehakjain(pdf)1.json → mehakjain(pdf)2.json ...")

if __name__ == "__main__":
    print("🚀 Debug Filename System Test Suite")
    print("Testing the new meaningful debug file naming system...")
    
    # Test filename generation logic
    test_filename_generation()
    
    # Test the actual system
    test_debug_filename_system()
    
    print("\n" + "=" * 50)
    print("🎉 Testing completed!")
    print("\nKey Features Tested:")
    print("✅ Meaningful filenames based on source file")
    print("✅ Organized in debug_json_files folder")
    print("✅ Collision handling with incremental numbers")
    print("✅ Enhanced debug file listing and access")
    print("✅ Improved cleanup functionality")
    print("\nNext steps:")
    print("1. Process resumes that cause JSON errors")
    print("2. Check debug_json_files folder for meaningful names")
    print("3. Use debug endpoints to analyze and fix issues")
