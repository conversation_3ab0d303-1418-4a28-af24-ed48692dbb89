#!/usr/bin/env python3
"""
Test script for the /interfix endpoint
"""

import requests
import json

# API endpoint
BASE_URL = "http://localhost:8000"
INTERFIX_URL = f"{BASE_URL}/interfix"

def test_interfix_endpoint():
    """Test the /interfix endpoint with sample call summary data"""
    
    # Test case 1: Complete call summary with all information
    test_case_1 = {
        "summary": """
        The call was an automated HR screening for an AI-powered full stack developer position. 
        The candidate indicated they have a 2-month notice period, expect a salary of 1 lakh rupees monthly, 
        and are seeking to change jobs primarily to relocate to the company's location. 
        They expressed flexibility for both in-office and remote work arrangements.
        The candidate mentioned they have an offer in hand worth 80000 rupees per month.
        They prefer to have interviews in the morning, around 10:00 AM, and are available next week for interviews.
        """
    }
    
    # Test case 2: Partial information
    test_case_2 = {
        "summary": """
        During the screening call, the candidate mentioned they are currently serving a 30-day notice period.
        They are looking for a career change to get better growth opportunities.
        No specific salary expectations or interview preferences were discussed.
        """
    }
    
    # Test case 3: Minimal information
    test_case_3 = {
        "summary": """
        Brief call with candidate. They seem interested in the position.
        No specific details about notice period, salary, or availability were discussed.
        """
    }
    
    test_cases = [
        ("Complete Information", test_case_1),
        ("Partial Information", test_case_2),
        ("Minimal Information", test_case_3)
    ]
    
    print("Testing /interfix endpoint...")
    print("=" * 50)
    
    for test_name, test_data in test_cases:
        print(f"\n🧪 Test Case: {test_name}")
        print("-" * 30)
        
        try:
            # Make the API request
            response = requests.post(
                INTERFIX_URL,
                json=test_data,
                headers={"Content-Type": "application/json"},
                timeout=120  # 2 minute timeout for AI processing
            )
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Success! Extracted information:")
                print(json.dumps(result, indent=2))
            else:
                print(f"❌ Error: {response.status_code}")
                print(f"Response: {response.text}")
                
        except requests.exceptions.Timeout:
            print("❌ Request timed out")
        except requests.exceptions.ConnectionError:
            print("❌ Could not connect to the API server")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
    
    print("\n" + "=" * 50)
    print("Testing completed!")

def test_root_endpoint():
    """Test that the root endpoint includes the new /interfix endpoint"""
    print("\n🔍 Testing root endpoint for /interfix listing...")
    
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code == 200:
            data = response.json()
            endpoints = data.get("endpoints", [])
            
            # Check if /interfix is listed
            interfix_found = False
            for endpoint in endpoints:
                if endpoint.get("path") == "/interfix":
                    interfix_found = True
                    print("✅ /interfix endpoint found in root listing:")
                    print(json.dumps(endpoint, indent=2))
                    break
            
            if not interfix_found:
                print("❌ /interfix endpoint not found in root listing")
        else:
            print(f"❌ Root endpoint error: {response.status_code}")
    except Exception as e:
        print(f"❌ Error testing root endpoint: {e}")

if __name__ == "__main__":
    # Test root endpoint first
    test_root_endpoint()
    
    # Test the interfix endpoint
    test_interfix_endpoint()
