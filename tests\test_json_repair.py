#!/usr/bin/env python3
"""
Test script for the new LLM JSON repair functionality
"""

import requests
import json

# API endpoint
BASE_URL = "http://localhost:8000"
REPAIR_URL = f"{BASE_URL}/debug/repair-json"

def test_json_repair():
    """Test the LLM JSON repair endpoint with sample malformed JSON"""
    
    # Sample malformed JSON (similar to what you're experiencing)
    malformed_json = """{
  "name": "<PERSON> Doe",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "education": [
    {
      "degree": "Bachelor of Science",
      "institution": "Example University",
      "year": "2018-2022"
    }
  ],
  "skills": [
    "Python",
    "JavaScript", 
    "React",
    "Node.js",
    "SQL",
    "MongoDB",
    "AWS",
    "Docker",
    "Kubernetes",
    "Git",
    "Jenkins",
    "Seleniums"
  ]
}"""  # Missing comma after the skills array - this will cause the delimiter error

    print("🧪 Testing LLM JSON Repair Functionality")
    print("=" * 50)
    
    print(f"\n📝 Malformed JSON (length: {len(malformed_json)}):")
    print(malformed_json[:200] + "..." if len(malformed_json) > 200 else malformed_json)
    
    # Test if it's actually malformed
    try:
        json.loads(malformed_json)
        print("⚠️  JSON is actually valid - adding deliberate error...")
        # Add a deliberate error by removing a comma
        malformed_json = malformed_json.replace('"Seleniums"', '"Seleniums"') + '\n  "experience": []'  # Missing comma
    except json.JSONDecodeError as e:
        print(f"✅ JSON is malformed as expected: {e}")
    
    # Test the repair endpoint
    try:
        print("\n🔧 Sending to LLM for repair...")
        
        response = requests.post(
            REPAIR_URL,
            json={
                "malformed_json": malformed_json,
                "context": "resume"
            },
            headers={"Content-Type": "application/json"},
            timeout=60  # 1 minute timeout
        )
        
        print(f"📡 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Repair Status: {result['status']}")
            print(f"📄 Message: {result['message']}")
            
            if result['status'] == 'repaired':
                print(f"\n🎉 SUCCESS! JSON was repaired by LLM")
                print(f"📊 Repair Stats:")
                stats = result.get('repair_stats', {})
                print(f"   - Original length: {stats.get('original_length', 'N/A')}")
                print(f"   - Repaired length: {stats.get('repaired_length', 'N/A')}")
                print(f"   - Context: {stats.get('context', 'N/A')}")
                
                print(f"\n🔍 Repaired JSON preview:")
                repaired = result.get('repaired_json', '')
                print(repaired[:300] + "..." if len(repaired) > 300 else repaired)
                
                # Validate the repaired JSON
                try:
                    parsed = json.loads(repaired)
                    print(f"\n✅ Repaired JSON is valid!")
                    print(f"📋 Extracted fields: {list(parsed.keys())}")
                except json.JSONDecodeError as e:
                    print(f"\n❌ Repaired JSON is still invalid: {e}")
                    
            elif result['status'] == 'already_valid':
                print(f"\n💡 JSON was already valid")
            else:
                print(f"\n❌ Repair failed: {result.get('message', 'Unknown error')}")
                if 'repair_error' in result:
                    print(f"🔍 Repair error: {result['repair_error']}")
                    
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out - LLM repair took too long")
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to the API server")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def test_with_actual_debug_file():
    """Test repair with an actual debug file if available"""
    print("\n" + "=" * 50)
    print("🔍 Testing with actual debug files...")
    
    try:
        # Get list of debug files
        response = requests.get(f"{BASE_URL}/debug/json-files")
        if response.status_code == 200:
            debug_data = response.json()
            debug_files = debug_data.get('debug_files', [])
            
            if debug_files:
                print(f"📁 Found {len(debug_files)} debug files")
                
                # Test with the most recent debug file
                latest_file = debug_files[0]
                filename = latest_file['filename']
                print(f"🧪 Testing repair with: {filename}")
                
                # Get the debug file content
                file_response = requests.get(f"{BASE_URL}/debug/json-files/{filename}")
                if file_response.status_code == 200:
                    file_data = file_response.json()
                    malformed_content = file_data.get('content', '')
                    
                    if malformed_content:
                        print(f"📄 Debug file content length: {len(malformed_content)}")
                        
                        # Test repair
                        repair_response = requests.post(
                            REPAIR_URL,
                            json={
                                "malformed_json": malformed_content,
                                "context": "resume"
                            },
                            timeout=60
                        )
                        
                        if repair_response.status_code == 200:
                            repair_result = repair_response.json()
                            print(f"🔧 Repair result: {repair_result['status']}")
                            print(f"📝 Message: {repair_result['message']}")
                        else:
                            print(f"❌ Repair failed: {repair_response.status_code}")
                    else:
                        print("❌ Debug file is empty")
                else:
                    print(f"❌ Could not read debug file: {file_response.status_code}")
            else:
                print("📭 No debug files found - try processing a resume first to generate some")
        else:
            print(f"❌ Could not get debug files: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing with debug files: {e}")

if __name__ == "__main__":
    print("🚀 LLM JSON Repair Test Suite")
    print("Testing the new self-healing JSON functionality...")
    
    # Test 1: Basic repair functionality
    test_json_repair()
    
    # Test 2: Real debug files
    test_with_actual_debug_file()
    
    print("\n" + "=" * 50)
    print("🎉 Testing completed!")
    print("\nNext steps:")
    print("1. Try processing a resume that previously failed")
    print("2. Check the logs for 'LLM self-healing' messages")
    print("3. Monitor the success rate improvement")
