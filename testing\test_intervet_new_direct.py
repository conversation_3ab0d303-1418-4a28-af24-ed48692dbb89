#!/usr/bin/env python3
"""
Direct test of the intervet_new functions without starting the server
"""

import sys
import os
import json
import time

# Add the parent directory to the path so we can import from main
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from main import (
    WeightageConfig, 
    IntervetNewRequest, 
    IntervetNewResponse, 
    FieldScore,
    calculate_candidate_job_fit_new,
    calculate_skills_score_new,
    calculate_experience_score_new,
    calculate_education_score_new,
    calculate_certifications_score_new,
    calculate_location_score_new,
    calculate_reliability_score_new,
    log_intervet_new_calculation
)

def get_sample_resume_data():
    """Get sample resume data for testing"""
    return {
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "phone": "+1234567890",
        "location": "San Francisco, CA",
        "summary": "Experienced software engineer with 5+ years in full-stack development",
        "education": [
            {
                "degree": "B.Tech Computer Science",
                "institution": "Stanford University",
                "year": "2018-2022",
                "gpa": "3.8/4.0"
            }
        ],
        "skills": [
            "Python", "JavaScript", "React", "Node.js", "SQL", "AWS", 
            "Docker", "Git", "REST APIs", "Machine Learning"
        ],
        "experience": [
            {
                "company_name": "Tech Corp",
                "role": "Senior Software Engineer",
                "duration": "2022-Present",
                "location": "San Francisco, CA",
                "key_responsibilities": "Developed full-stack applications using React and Node.js, implemented REST APIs, worked with AWS services"
            },
            {
                "company_name": "StartupXYZ",
                "role": "Software Developer",
                "duration": "2020-2022",
                "location": "San Francisco, CA",
                "key_responsibilities": "Built web applications using Python and JavaScript, worked with SQL databases"
            }
        ],
        "projects": [
            {
                "name": "E-commerce Platform",
                "description": "Built a full-stack e-commerce platform using React, Node.js, and MongoDB",
                "technologies_used": ["React", "Node.js", "MongoDB", "Express"]
            }
        ],
        "certifications": [
            {
                "name": "AWS Certified Developer",
                "issuer": "Amazon Web Services",
                "date": "2023"
            }
        ]
    }

def get_sample_jd_data():
    """Get sample job description data for testing"""
    return {
        "job_title": "Full Stack Developer",
        "company_name": "TechCorp Solutions",
        "location": "San Francisco, CA",
        "required_skills": [
            "Python", "JavaScript", "React", "Node.js", "SQL", 
            "REST APIs", "Git", "AWS"
        ],
        "preferred_skills": [
            "Docker", "Kubernetes", "Machine Learning", "TypeScript"
        ],
        "required_experience": "3+ years of experience in full-stack development",
        "education_requirements": [
            "Bachelor's degree in Computer Science or related field"
        ]
    }

def test_individual_scoring_functions():
    """Test each scoring function individually"""
    print("🧪 Testing Individual Scoring Functions")
    print("=" * 50)
    
    resume_data = get_sample_resume_data()
    jd_data = get_sample_jd_data()
    
    # Test skills scoring
    print("\n📊 Testing Skills Scoring...")
    skills_score, skills_rationale, skills_details = calculate_skills_score_new(resume_data, jd_data)
    print(f"   Score: {skills_score:.2f}/10")
    print(f"   Rationale: {skills_rationale}")
    print(f"   Details: {skills_details}")
    
    # Test experience scoring
    print("\n📊 Testing Experience Scoring...")
    exp_score, exp_rationale, exp_details = calculate_experience_score_new(resume_data, jd_data)
    print(f"   Score: {exp_score:.2f}/10")
    print(f"   Rationale: {exp_rationale}")
    print(f"   Details: {exp_details}")
    
    # Test education scoring
    print("\n📊 Testing Education Scoring...")
    edu_score, edu_rationale, edu_details = calculate_education_score_new(resume_data, jd_data)
    print(f"   Score: {edu_score:.2f}/10")
    print(f"   Rationale: {edu_rationale}")
    print(f"   Details: {edu_details}")
    
    # Test certifications scoring
    print("\n📊 Testing Certifications Scoring...")
    cert_score, cert_rationale, cert_details = calculate_certifications_score_new(resume_data, jd_data)
    print(f"   Score: {cert_score:.2f}/10")
    print(f"   Rationale: {cert_rationale}")
    print(f"   Details: {cert_details}")
    
    # Test location scoring
    print("\n📊 Testing Location Scoring...")
    loc_score, loc_rationale, loc_details = calculate_location_score_new(resume_data, jd_data)
    print(f"   Score: {loc_score:.2f}/10")
    print(f"   Rationale: {loc_rationale}")
    print(f"   Details: {loc_details}")
    
    # Test reliability scoring
    print("\n📊 Testing Reliability Scoring...")
    rel_score, rel_rationale, rel_details = calculate_reliability_score_new(resume_data, jd_data)
    print(f"   Score: {rel_score:.2f}/10")
    print(f"   Rationale: {rel_rationale}")
    print(f"   Details: {rel_details}")
    
    return True

def test_cgpa_calculation():
    """Test the main CGPA calculation function"""
    print("\n🧪 Testing CGPA Calculation")
    print("=" * 50)
    
    resume_data = get_sample_resume_data()
    jd_data = get_sample_jd_data()
    
    # Test with default weights
    print("\n📈 Test 1: Default Weights")
    print("-" * 30)
    
    start_time = time.time()
    result = calculate_candidate_job_fit_new(resume_data, jd_data)
    processing_time = time.time() - start_time
    
    print(f"✅ Calculation completed in {processing_time:.3f} seconds")
    print(f"📊 Total Score: {result.total_score:.2f}/10")
    print(f"🎯 Fit Category: {result.fit_category}")
    print(f"⚖️  Total Credits: {result.total_credits_used}")
    print(f"💭 Summary: {result.summary}")
    
    print(f"\n📋 Field Breakdown:")
    fields = [
        ("Skills", result.skills_score),
        ("Experience", result.experience_score),
        ("Education", result.education_score),
        ("Certifications", result.certifications_score),
        ("Location", result.location_score),
        ("Reliability", result.reliability_score)
    ]
    
    for field_name, field_score in fields:
        print(f"   {field_name}: {field_score.raw_score:.1f}/10 (weight: {field_score.weight}, weighted: {field_score.weighted_score:.1f})")
    
    # Test with custom weights
    print("\n📈 Test 2: Skills-Heavy Weights")
    print("-" * 30)
    
    custom_weights = WeightageConfig(
        skills=5.0,
        experience=2.0,
        education=1.5,
        certifications=1.0,
        location=0.5,
        reliability=0.0
    )
    
    start_time = time.time()
    result2 = calculate_candidate_job_fit_new(resume_data, jd_data, custom_weights)
    processing_time = time.time() - start_time
    
    print(f"✅ Calculation completed in {processing_time:.3f} seconds")
    print(f"📊 Total Score: {result2.total_score:.2f}/10")
    print(f"🎯 Fit Category: {result2.fit_category}")
    print(f"⚖️  Total Credits: {result2.total_credits_used}")
    
    return True

def test_logging_system():
    """Test the comprehensive logging system"""
    print("\n🧪 Testing Logging System")
    print("=" * 50)
    
    resume_data = get_sample_resume_data()
    jd_data = get_sample_jd_data()
    weightage = WeightageConfig()
    
    # Calculate result
    result = calculate_candidate_job_fit_new(resume_data, jd_data, weightage)
    
    # Test logging
    print("📁 Testing comprehensive logging...")
    log_folder = log_intervet_new_calculation(
        resume_data=resume_data,
        jd_data=jd_data,
        weightage=weightage,
        result=result,
        request_id="test_001"
    )
    
    if log_folder and os.path.exists(log_folder):
        print(f"✅ Logging successful! Files created in: {log_folder}")
        
        # List created files
        files = os.listdir(log_folder)
        print(f"📄 Created {len(files)} log files:")
        for file in sorted(files):
            file_path = os.path.join(log_folder, file)
            file_size = os.path.getsize(file_path)
            print(f"   - {file} ({file_size} bytes)")
        
        return True
    else:
        print("❌ Logging failed!")
        return False

def main():
    """Run all direct tests"""
    print("🚀 Direct Testing of /intervet_new Functions")
    print("=" * 60)
    
    tests = [
        ("Individual Scoring Functions", test_individual_scoring_functions),
        ("CGPA Calculation", test_cgpa_calculation),
        ("Logging System", test_logging_system)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔍 Running: {test_name}")
            if test_func():
                print(f"✅ {test_name}: PASSED")
                passed += 1
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n📊 FINAL RESULTS")
    print("=" * 60)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! The intervet_new implementation is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
    
    print(f"\n📁 Check the 'intervet_new_logs' folder for detailed calculation logs.")

if __name__ == "__main__":
    main()
