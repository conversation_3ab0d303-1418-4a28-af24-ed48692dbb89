{
  "experience": [
    {
      "title": "Java/J2EE Developer",
      "company": "Target",
      "location": "Minneapolis, MN",
      "duration": "Dec 2015 to Feb 2018",
      "responsibilities": [
        "Engineered microservices architecture using J2EE and Spring, ensuring modular and maintainable backend solutions.",
        "Developed an event-driven microservices architecture using Apache Kafka, enabling real-time data streaming and processing for enhanced scalability and reliability.",
        "Enhanced legacy applications by migrating from JSP to modern frontend frameworks like Angular, improving user interface responsiveness and user experience.",
        "Integrated Struts and Spring MVC frameworks for developing RESTful APIs, facilitating seamless data exchange between frontend and backend systems.",
        "Implemented Spring Boot for rapid application development, reducing time-to-market for new features and enhancements.",
        "Configured and optimized Oracle WebLogic servers for deploying enterprise-grade Java applications, ensuring high availability and performance.",
        "Utilized Bootstrap for designing responsive web interfaces, ensuring cross-browser compatibility and adherence to UX design principles.",
        "Deployed applications on Azure cloud, utilizing Azure App Service and Azure SQL Database, and maintained distributed applications with Cassandra, ensuring data consistency and availability across multi-node clusters.",
        "Implemented logging and monitoring using Log4j and Visual Studio, ensuring real-time visibility into application performance and behavior.",
        "Integrated Hibernate with JPA for object-relational mapping, optimizing database interactions and improving data access performance.",
        "Collaborated in Agile Scrum teams using JIRA and Confluence, participating in sprint planning, daily stand-ups, and sprint reviews."
      ],
      "technologies": [
        "J2EE",
        "Spring",
        "Spring Boot",
        "Spring MVC",
        "Spring Data",
        "Spring Security",
        "JPA",
        "Hibernate",
        "JavaScript",
        "AJAX",
        "jQuery",
        "HTML",
        "CSS",
        "Gradle",
        "JUnit",
        "Visual Studio",
        "JIRA",
        "Confluence"
      ]
    }