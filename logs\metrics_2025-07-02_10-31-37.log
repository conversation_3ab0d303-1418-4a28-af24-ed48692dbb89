{"event": "session_start", "session_id": "13928ead-b769-49b0-9858-ca6793455b39", "timestamp": "2025-07-02T10:31:37.076508", "message": "New API session started"}
{"event": "request_start", "session_id": "13928ead-b769-49b0-9858-ca6793455b39", "request_id": "3d935bb3-a91c-4398-8924-8d4aa2024744", "endpoint": "/", "timestamp": "2025-07-02T10:31:39.762253", "message": "Request started for endpoint: /"}
{"event": "request_complete", "session_id": "13928ead-b769-49b0-9858-ca6793455b39", "request_id": "3d935bb3-a91c-4398-8924-8d4aa2024744", "endpoint": "/", "timestamp": "2025-07-02T10:31:39.765952", "total_time_seconds": 0.003699779510498047, "status_code": 200, "message": "Request completed in 0.0037s with status 200"}
{"event": "request_start", "session_id": "13928ead-b769-49b0-9858-ca6793455b39", "request_id": "ca748e07-2d32-4005-8cc8-2fee1e21e785", "endpoint": "/favicon.ico", "timestamp": "2025-07-02T10:31:40.009661", "message": "Request started for endpoint: /favicon.ico"}
{"event": "request_complete", "session_id": "13928ead-b769-49b0-9858-ca6793455b39", "request_id": "ca748e07-2d32-4005-8cc8-2fee1e21e785", "endpoint": "/favicon.ico", "timestamp": "2025-07-02T10:31:40.014907", "total_time_seconds": 0.005245685577392578, "status_code": 404, "message": "Request completed in 0.0052s with status 404"}
{"event": "request_start", "session_id": "13928ead-b769-49b0-9858-ca6793455b39", "request_id": "4eb6b118-5493-499d-b61f-025bec875698", "endpoint": "/docs", "timestamp": "2025-07-02T10:31:50.956893", "message": "Request started for endpoint: /docs"}
{"event": "request_complete", "session_id": "13928ead-b769-49b0-9858-ca6793455b39", "request_id": "4eb6b118-5493-499d-b61f-025bec875698", "endpoint": "/docs", "timestamp": "2025-07-02T10:31:50.963542", "total_time_seconds": 0.006649494171142578, "status_code": 200, "message": "Request completed in 0.0066s with status 200"}
{"event": "request_start", "session_id": "13928ead-b769-49b0-9858-ca6793455b39", "request_id": "6a448c0f-d0e0-4981-9209-6bf695742421", "endpoint": "/openapi.json", "timestamp": "2025-07-02T10:31:51.393494", "message": "Request started for endpoint: /openapi.json"}
{"event": "request_complete", "session_id": "13928ead-b769-49b0-9858-ca6793455b39", "request_id": "6a448c0f-d0e0-4981-9209-6bf695742421", "endpoint": "/openapi.json", "timestamp": "2025-07-02T10:31:51.591859", "total_time_seconds": 0.1983642578125, "status_code": 200, "message": "Request completed in 0.1984s with status 200"}
{"event": "request_start", "session_id": "13928ead-b769-49b0-9858-ca6793455b39", "request_id": "3bb80caf-6b34-4dad-9b28-563eb5149219", "endpoint": "/hybrid_resume", "timestamp": "2025-07-02T10:32:27.415735", "message": "Request started for endpoint: /hybrid_resume"}
{"event": "custom_metric", "session_id": "13928ead-b769-49b0-9858-ca6793455b39", "request_id": "3bb80caf-6b34-4dad-9b28-563eb5149219", "endpoint": "/hybrid_resume", "timestamp": "2025-07-02T10:32:27.452331", "file_type": "pdf", "message": "Custom metric: file_type=pdf"}
{"event": "custom_metric", "session_id": "13928ead-b769-49b0-9858-ca6793455b39", "request_id": "3bb80caf-6b34-4dad-9b28-563eb5149219", "endpoint": "/hybrid_resume", "timestamp": "2025-07-02T10:32:27.452331", "file_size_bytes": 60820, "message": "Custom metric: file_size_bytes=60820"}
{"event": "custom_metric", "session_id": "13928ead-b769-49b0-9858-ca6793455b39", "request_id": "3bb80caf-6b34-4dad-9b28-563eb5149219", "endpoint": "/hybrid_resume", "timestamp": "2025-07-02T10:32:27.452331", "extraction_method": "pdf_text", "message": "Custom metric: extraction_method=pdf_text"}
{"event": "custom_metric", "session_id": "13928ead-b769-49b0-9858-ca6793455b39", "request_id": "3bb80caf-6b34-4dad-9b28-563eb5149219", "endpoint": "/hybrid_resume", "timestamp": "2025-07-02T10:32:27.452331", "extracted_text_length": 2804, "message": "Custom metric: extracted_text_length=2804"}
{"event": "custom_metric", "session_id": "13928ead-b769-49b0-9858-ca6793455b39", "request_id": "3bb80caf-6b34-4dad-9b28-563eb5149219", "endpoint": "/hybrid_resume", "timestamp": "2025-07-02T10:32:27.452331", "file_processing_time": 0.030073165893554688, "message": "Custom metric: file_processing_time=0.030073165893554688"}
{"event": "request_complete", "session_id": "13928ead-b769-49b0-9858-ca6793455b39", "request_id": "3bb80caf-6b34-4dad-9b28-563eb5149219", "endpoint": "/hybrid_resume", "timestamp": "2025-07-02T10:33:03.929557", "total_time_seconds": 36.51382255554199, "status_code": 200, "message": "Request completed in 36.5138s with status 200"}
{"event": "request_start", "session_id": "13928ead-b769-49b0-9858-ca6793455b39", "request_id": "94f52e49-1f01-4356-bac3-da94a1b54baa", "endpoint": "/jd_parser", "timestamp": "2025-07-02T10:33:03.930735", "message": "Request started for endpoint: /jd_parser"}
{"event": "request_complete", "session_id": "13928ead-b769-49b0-9858-ca6793455b39", "request_id": "94f52e49-1f01-4356-bac3-da94a1b54baa", "endpoint": "/jd_parser", "timestamp": "2025-07-02T10:33:12.629052", "total_time_seconds": 8.698317766189575, "status_code": 200, "message": "Request completed in 8.6983s with status 200"}
{"event": "request_start", "session_id": "13928ead-b769-49b0-9858-ca6793455b39", "request_id": "ea9c4894-39eb-424e-8327-e6baf80630dd", "endpoint": "/intervet_new", "timestamp": "2025-07-02T10:33:54.841012", "message": "Request started for endpoint: /intervet_new"}
{"event": "custom_metric", "session_id": "13928ead-b769-49b0-9858-ca6793455b39", "request_id": "ea9c4894-39eb-424e-8327-e6baf80630dd", "endpoint": "/intervet_new", "timestamp": "2025-07-02T10:33:54.842521", "endpoint_type": "intervet_new", "message": "Custom metric: endpoint_type=intervet_new"}
{"event": "custom_metric", "session_id": "13928ead-b769-49b0-9858-ca6793455b39", "request_id": "ea9c4894-39eb-424e-8327-e6baf80630dd", "endpoint": "/intervet_new", "timestamp": "2025-07-02T10:33:54.855799", "final_score": 4.63, "message": "Custom metric: final_score=4.63"}
{"event": "custom_metric", "session_id": "13928ead-b769-49b0-9858-ca6793455b39", "request_id": "ea9c4894-39eb-424e-8327-e6baf80630dd", "endpoint": "/intervet_new", "timestamp": "2025-07-02T10:33:54.855799", "fit_category": "Moderate Match", "message": "Custom metric: fit_category=Moderate Match"}
{"event": "custom_metric", "session_id": "13928ead-b769-49b0-9858-ca6793455b39", "request_id": "ea9c4894-39eb-424e-8327-e6baf80630dd", "endpoint": "/intervet_new", "timestamp": "2025-07-02T10:33:54.855799", "total_credits_used": 10.0, "message": "Custom metric: total_credits_used=10.0"}
{"event": "custom_metric", "session_id": "13928ead-b769-49b0-9858-ca6793455b39", "request_id": "ea9c4894-39eb-424e-8327-e6baf80630dd", "endpoint": "/intervet_new", "timestamp": "2025-07-02T10:33:54.856799", "log_folder": "intervet_new_logs\\intervet_new_20250702_103354_850_1751432634850", "message": "Custom metric: log_folder=intervet_new_logs\\intervet_new_20250702_103354_850_1751432634850"}
{"event": "request_complete", "session_id": "13928ead-b769-49b0-9858-ca6793455b39", "request_id": "ea9c4894-39eb-424e-8327-e6baf80630dd", "endpoint": "/intervet_new", "timestamp": "2025-07-02T10:33:54.856799", "total_time_seconds": 0.015787363052368164, "status_code": 200, "message": "Request completed in 0.0158s with status 200"}
{"event": "session_end", "session_id": "13928ead-b769-49b0-9858-ca6793455b39", "timestamp": "2025-07-02T10:40:50.382653", "message": "API session ended"}
