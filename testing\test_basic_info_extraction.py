#!/usr/bin/env python3
"""
Test script for the new basic info extraction functionality.
Demonstrates how the regex method now extracts name, email, phone, and role.
"""

import sys
import os

# Add the current directory to the path so we can import from main.py
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import extract_sections_regex, extract_basic_info_smart, parse_basic_info_components

def test_basic_info_extraction():
    """Test basic info extraction with different resume formats."""
    
    print("🧪 Testing Smart Basic Info Extraction")
    print("=" * 70)
    print("Testing extraction of name, email, phone, and role from resume headers")
    print()
    
    # Test cases with different header formats
    test_cases = [
        {
            "name": "Format 1: Traditional Layout",
            "text": """<PERSON>
Senior Software Engineer
Email: <EMAIL>
Phone: (*************

SUMMARY
Experienced software engineer with 5+ years...

EDUCATION
Bachelor of Science in Computer Science..."""
        },
        {
            "name": "Format 2: Compact <PERSON><PERSON>",
            "text": """<PERSON> | Full Stack Developer | <EMAIL> | ******-987-6543

PROFESSIONAL SUMMARY
Results-driven developer with expertise..."""
        },
        {
            "name": "Format 3: Social Links Included",
            "text": """<PERSON>
Lead Data Scientist
LinkedIn: linkedin.com/in/mjohnson
GitHub: github.com/mjohnson
Email: <EMAIL>
Phone: ************

OBJECTIVE
Passionate data scientist seeking..."""
        },
        {
            "name": "Format 4: Role After Contact Info",
            "text": """Sarah Wilson
Email: <EMAIL>
Phone: +****************
Senior Product Manager

EXPERIENCE
Product Manager at TechCorp..."""
        },
        {
            "name": "Format 5: International Format",
            "text": """Raj Patel
Software Development Engineer
Email: <EMAIL>
Mobile: +91 9876543210
Location: Bangalore, India

PROFESSIONAL SUMMARY
Experienced engineer with 8+ years..."""
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"📄 Test Case {i}: {test_case['name']}")
        print("-" * 50)
        
        # Extract basic info
        basic_info, confidence = extract_basic_info_smart(test_case['text'])
        print(f"📝 Raw Basic Info (Confidence: {confidence:.2f}):")
        print(f"   {repr(basic_info[:100])}...")
        
        # Parse components
        components = parse_basic_info_components(basic_info)
        print(f"\n🔍 Parsed Components:")
        
        for component, value in components.items():
            status = "✅" if value else "❌"
            print(f"   {status} {component.capitalize()}: {value or 'Not found'}")
        
        # Test full extraction
        sections, confidence_scores = extract_sections_regex(test_case['text'], f"test_case_{i}.txt", "")
        
        print(f"\n📊 Full Extraction Results:")
        basic_sections = {k: v for k, v in sections.items() if k.startswith('basic_')}
        for section_name, content in basic_sections.items():
            conf = confidence_scores.get(section_name, 0.0)
            print(f"   {section_name}: {content} (conf: {conf:.2f})")
        
        print("\n" + "=" * 70)

def test_with_real_resume():
    """Test with a real resume file."""
    
    print("\n🔍 Testing with Real Resume Files")
    print("=" * 70)
    
    # Test with actual resume files
    test_files = [
        "resume_extracted_text/Balpreet_SrFullstack_Java.txt",
        "resume_extracted_text/Resume-Raman_Luhach.txt"
    ]
    
    for file_path in test_files:
        if not os.path.exists(file_path):
            print(f"❌ File not found: {file_path}")
            continue
        
        print(f"📄 Testing with: {os.path.basename(file_path)}")
        print("-" * 50)
        
        # Read the file
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract the actual resume text (skip debug header)
        lines = content.split('\n')
        resume_text = ""
        start_extracting = False
        
        for line in lines:
            if line.startswith("# ================================================"):
                start_extracting = True
                continue
            if start_extracting:
                resume_text += line + "\n"
        
        # Extract basic info
        basic_info, confidence = extract_basic_info_smart(resume_text)
        print(f"📝 Basic Info Extracted (Confidence: {confidence:.2f}):")
        print(f"   Length: {len(basic_info)} characters")
        
        # Show first few lines
        basic_lines = basic_info.split('\n')[:5]
        for line in basic_lines:
            if line.strip():
                print(f"   {line}")
        
        # Parse components
        components = parse_basic_info_components(basic_info)
        print(f"\n🔍 Parsed Components:")
        
        for component, value in components.items():
            status = "✅" if value else "❌"
            print(f"   {status} {component.capitalize()}: {value or 'Not found'}")
        
        # Test full extraction
        sections, confidence_scores = extract_sections_regex(resume_text, os.path.basename(file_path), "")
        
        print(f"\n📊 All Sections Found: {len(sections)}")
        for section_name in sections.keys():
            conf = confidence_scores.get(section_name, 0.0)
            content_length = len(sections[section_name]) if sections[section_name] else 0
            status = "✅" if content_length > 0 else "❌"
            print(f"   {status} {section_name}: {content_length} chars (conf: {conf:.2f})")
        
        print("\n" + "=" * 70)

def demonstrate_smart_features():
    """Demonstrate the smart features of basic info extraction."""
    
    print("\n🧠 Smart Features Demonstration")
    print("=" * 70)
    
    features = [
        {
            "feature": "Header Boundary Detection",
            "description": "Automatically finds where basic info ends and formal sections begin",
            "example": "Stops at 'SUMMARY' or 'EDUCATION' headers"
        },
        {
            "feature": "Pattern Recognition",
            "description": "Uses regex patterns to identify email, phone, and role",
            "example": "Recognizes various phone formats: (*************, ******-123-4567, ************"
        },
        {
            "feature": "Name Extraction Logic",
            "description": "Identifies name as first meaningful line without keywords",
            "example": "Skips lines with 'email', 'phone', 'linkedin', '@', '+'"
        },
        {
            "feature": "Role Detection",
            "description": "Finds job titles using keyword matching",
            "example": "Detects 'developer', 'engineer', 'manager', 'analyst', etc."
        },
        {
            "feature": "Confidence Scoring",
            "description": "Calculates confidence based on pattern matches and content quality",
            "example": "Higher confidence for valid email/phone patterns"
        },
        {
            "feature": "Fallback Handling",
            "description": "Uses first 15 lines if no formal sections found",
            "example": "Works even with non-standard resume formats"
        }
    ]
    
    for i, feature in enumerate(features, 1):
        print(f"{i}. 🎯 {feature['feature']}")
        print(f"   📝 {feature['description']}")
        print(f"   💡 Example: {feature['example']}")
        print()

if __name__ == "__main__":
    test_basic_info_extraction()
    test_with_real_resume()
    demonstrate_smart_features()
    
    print("🎉 Basic Info Extraction Testing Complete!")
    
    print("\n📚 Key Benefits:")
    print("✅ Automatically extracts name, email, phone, role")
    print("✅ Works with various resume header formats")
    print("✅ Smart boundary detection (stops at formal sections)")
    print("✅ High confidence scoring for pattern matches")
    print("✅ Fallback handling for non-standard formats")
    print("✅ Individual component access (basic_name, basic_email, etc.)")
    
    print("\n🚀 API Usage:")
    print("   • POST /section3 now returns basic_info section")
    print("   • Individual components: basic_name, basic_email, basic_phone, basic_role")
    print("   • High confidence scores for well-formatted contact info")
    print("   • Works seamlessly with existing section extraction")
