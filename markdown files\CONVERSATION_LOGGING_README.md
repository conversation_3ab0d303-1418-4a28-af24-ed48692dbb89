# Conversation Logging for Section Extraction

This document describes the conversation logging feature that tracks all LLM calls made during resume section extraction, providing comprehensive debugging and analysis capabilities.

## Overview

The conversation logging system creates dedicated folders for each resume extraction session, capturing:
- All individual LLM calls with prompts and responses
- Processing times and confidence scores
- Error logs and debugging information
- Final extraction results summary
- Complete conversation timeline

## Directory Structure

```
conv/
├── 20241217_143052_resume_name_multiple_calls_conversation/
│   ├── conversation_summary.txt          # Initial conversation setup
│   ├── call_01_summary_resume_name.txt   # Individual section calls
│   ├── call_02_education_resume_name.txt
│   ├── call_03_experience_resume_name.txt
│   ├── ...
│   └── final_results_summary.txt         # Final extraction results
└── 20241217_143155_resume_name_single_call_conversation/
    ├── conversation_summary.txt
    ├── call_01_all_sections_resume_name.txt  # Single comprehensive call
    └── final_results_summary.txt
```

## Folder Naming Convention

```
{timestamp}_{resume_name}_{extraction_method}_conversation
```

Where:
- **timestamp**: `YYYYMMDD_HHMMSS` format
- **resume_name**: Sanitized resume filename (without extension)
- **extraction_method**: `multiple_calls` or `single_call`

### Examples:
- `20241217_143052_john_doe_resume_multiple_calls_conversation`
- `20241217_143155_jane_smith_cv_single_call_conversation`

## File Types

### 1. conversation_summary.txt

Initial conversation setup and metadata:

```
================================================================================
RESUME SECTION EXTRACTION CONVERSATION
================================================================================
Source File: john_doe_resume.pdf
Extraction Method: multiple_calls
Started: 2024-12-17T14:30:52.123456
Conversation Folder: conv\20241217_143052_john_doe_resume_multiple_calls_conversation
================================================================================

This folder contains all LLM calls made for this resume extraction.
Each section extraction is logged as a separate file.
```

### 2. call_XX_section_name.txt

Individual LLM call logs:

```
================================================================================
SECTION EXTRACTION CALL #1 - SUMMARY
================================================================================
Source File: john_doe_resume.pdf
Section: summary
Call Number: 1
Timestamp: 2024-12-17T14:30:52.456789
Processing Time: 5.67 seconds
Confidence Score: 0.85
Model: gemma3:4b
================================================================================

[PROMPT]
Length: 1234 characters
----------------------------------------
Extract ONLY the professional summary, objective, or career summary section 
from this resume text. Return only the summary content without any section 
headers or additional text. If no summary is found, return 'NOT_FOUND'.

Resume Text:
John Doe
Software Engineer
...
----------------------------------------

[RESPONSE]
Length: 234 characters
Confidence: 0.85
----------------------------------------
Experienced software engineer with 5+ years of expertise in full-stack 
development, machine learning, and cloud technologies. Proven track record 
of delivering scalable solutions and leading cross-functional teams.
----------------------------------------

================================================================================
```

### 3. final_results_summary.txt

Comprehensive extraction results:

```
================================================================================
FINAL EXTRACTION RESULTS SUMMARY
================================================================================
Extraction Method: multiple_calls
Completed: 2024-12-17T14:31:45.789012
Processing Time: 53.25 seconds
Total LLM Calls: 8
Overall Confidence: 0.82
Sections Found: 7
================================================================================

SECTION EXTRACTION RESULTS:
----------------------------------------
SUMMARY: ✅ SUCCESS
  Confidence: 0.85
  Content Length: 234 characters
  Preview: Experienced software engineer with 5+ years of expertise...

EDUCATION: ✅ SUCCESS
  Confidence: 0.90
  Content Length: 156 characters
  Preview: Bachelor of Science in Computer Science...

EXPERIENCE: ✅ SUCCESS
  Confidence: 0.88
  Content Length: 567 characters
  Preview: Senior Software Engineer at TechCorp...

SKILLS: ✅ SUCCESS
  Confidence: 0.92
  Content Length: 123 characters
  Preview: Python, JavaScript, React, AWS, Docker...

PROJECTS: ✅ SUCCESS
  Confidence: 0.75
  Content Length: 345 characters
  Preview: E-commerce Platform - Built a full-stack...

CERTIFICATIONS: ✅ SUCCESS
  Confidence: 0.80
  Content Length: 89 characters
  Preview: AWS Certified Solutions Architect...

ACHIEVEMENTS: ❌ FAILED
  Confidence: 0.00
  Content Length: 0 characters

LANGUAGES: ✅ SUCCESS
  Confidence: 0.65
  Content Length: 45 characters
  Content: English: Native, Spanish: Conversational

================================================================================
All individual LLM calls are logged in separate files in this folder.
Check the numbered call files for detailed prompt/response logs.
```

## Method Differences

### Multiple Calls Method

**Folder Contents:**
- `conversation_summary.txt`
- `call_01_summary_resume.txt`
- `call_02_education_resume.txt`
- `call_03_experience_resume.txt`
- `call_04_skills_resume.txt`
- `call_05_projects_resume.txt`
- `call_06_certifications_resume.txt`
- `call_07_achievements_resume.txt`
- `call_08_languages_resume.txt`
- `final_results_summary.txt`

**Characteristics:**
- 8+ individual call files (one per section)
- Detailed prompt/response for each section
- Individual confidence scores and processing times
- Granular error tracking per section

### Single Call Method

**Folder Contents:**
- `conversation_summary.txt`
- `call_01_all_sections_resume.txt`
- `final_results_summary.txt`

**Characteristics:**
- 1 comprehensive call file
- All sections extracted in single prompt/response
- Consolidated logging approach
- Overall processing metrics

## Integration with Existing Logging

The conversation logging works alongside the existing prompt logging system:

- **Prompt Logs** (`prompt_logs/`): General LLM call logging across all endpoints
- **Conversation Logs** (`conv/`): Specific to section extraction with resume context
- **Section Results** (`resume sections extracted/`): Final extracted content files

## Usage Examples

### Analyzing a Failed Extraction

1. **Find the conversation folder** for the problematic resume
2. **Check `final_results_summary.txt`** for overall results
3. **Review individual call files** for sections that failed
4. **Examine prompts and responses** to identify issues
5. **Check confidence scores** to understand model certainty

### Comparing Extraction Methods

1. **Extract same resume** using both `/section` and `/section2`
2. **Compare conversation folders** for each method
3. **Analyze processing times** and call counts
4. **Review confidence scores** across methods
5. **Examine response quality** in individual call files

### Debugging Model Performance

1. **Review multiple conversation folders** for patterns
2. **Identify consistently failing sections** across resumes
3. **Analyze prompt effectiveness** from call files
4. **Track confidence score trends** over time
5. **Optimize prompts** based on response analysis

## Testing

### Using the Test Script

```bash
cd testing
python test_conversation_logging.py
```

This script will:
- Test both extraction methods with conversation logging
- Analyze conversation folder structure
- Compare logging between methods
- Show sample conversation content

### Manual Testing

```bash
# Test multiple calls method
curl -X POST "http://localhost:8000/section" -F "file=@resume.pdf"

# Test single call method  
curl -X POST "http://localhost:8000/section2" -F "file=@resume.pdf"

# Check conversation folders
ls -la conv/
```

## Benefits

1. **Complete Traceability**: Every LLM call is logged with full context
2. **Debugging Support**: Detailed error logs and response analysis
3. **Performance Monitoring**: Processing times and confidence tracking
4. **Method Comparison**: Side-by-side analysis of extraction approaches
5. **Quality Assurance**: Confidence scoring and content validation
6. **Resume-Specific Context**: Organized by individual resume files

## Best Practices

1. **Regular Cleanup**: Monitor `conv/` folder size and clean old conversations
2. **Error Analysis**: Review failed extractions to improve prompts
3. **Confidence Monitoring**: Track confidence trends to identify model issues
4. **Method Selection**: Use conversation logs to choose optimal extraction method
5. **Prompt Optimization**: Analyze successful calls to refine prompts

## File Management

### Automatic Cleanup

The system doesn't automatically clean conversation folders. Consider implementing:

```python
# Clean conversations older than 30 days
import shutil
from pathlib import Path
import time

conv_dir = Path("conv")
cutoff_time = time.time() - (30 * 24 * 60 * 60)

for folder in conv_dir.iterdir():
    if folder.is_dir() and folder.stat().st_mtime < cutoff_time:
        shutil.rmtree(folder)
```

### Storage Considerations

- Each conversation folder: ~50-500KB depending on method and content
- Multiple calls generate more files but smaller individual files
- Single calls generate fewer files but larger individual files
- Monitor disk usage for high-volume processing

This conversation logging system provides comprehensive visibility into the section extraction process, enabling detailed analysis and optimization of the Gemma model's performance on resume parsing tasks.
