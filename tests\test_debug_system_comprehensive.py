#!/usr/bin/env python3
"""
Comprehensive test of the debug filename system
"""

import requests
import json
import os

def test_json_repair_with_debug_files():
    """Test the JSON repair system and debug file creation"""
    
    print("🧪 Testing JSON Repair and Debug File System")
    print("=" * 60)
    
    # Test with malformed JSON that should create debug files
    malformed_json_samples = [
        {
            "name": "mehak_jain_resume.pdf",
            "json": """{
  "name": "<PERSON><PERSON>",
  "email": "<EMAIL>",
  "skills": ["Python", "Java", "JavaScript",],
  "experience": [
    {
      "company": "Tech Corp",
      "role": "Developer"
    }
  ]
}""",  # Trailing comma in skills array
        },
        {
            "name": "john_doe_cv.docx", 
            "json": """{
  "name": "<PERSON>",
  "email": "<EMAIL>"
  "phone": "+1234567890",
  "skills": ["Python", "React"]
}""",  # Missing comma after email
        },
        {
            "name": "candidate_resume.pdf",
            "json": """{
  "name": "<PERSON>",
  "skills": ["Java", "Spring"],
  "experience": [
    {
      "company": "StartupXYZ"
      "role": "Engineer",
      "duration": "2 years"
    }
  ]
}""",  # Missing comma after company
        }
    ]
    
    print("🔧 Testing JSON repair with different malformed samples...")
    
    for i, sample in enumerate(malformed_json_samples, 1):
        print(f"\n📄 Test {i}: {sample['name']}")
        print(f"JSON length: {len(sample['json'])} characters")
        
        try:
            # Test the repair endpoint
            response = requests.post(
                "http://localhost:8000/debug/repair-json",
                json={
                    "malformed_json": sample['json'],
                    "context": "resume"
                },
                timeout=60
            )
            
            print(f"  📡 Response Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"  🔧 Repair Status: {result['status']}")
                
                if result['status'] == 'repaired':
                    print(f"  ✅ Successfully repaired by LLM!")
                    stats = result.get('repair_stats', {})
                    print(f"     Original: {stats.get('original_length', 'N/A')} chars")
                    print(f"     Repaired: {stats.get('repaired_length', 'N/A')} chars")
                elif result['status'] == 'repair_failed':
                    print(f"  ❌ Repair failed: {result.get('message', 'Unknown error')}")
                else:
                    print(f"  ℹ️  Status: {result['status']}")
            else:
                print(f"  ❌ API Error: {response.status_code}")
                print(f"     Response: {response.text[:200]}")
                
        except Exception as e:
            print(f"  ❌ Error: {e}")
    
    # Check for any debug files that might have been created
    print(f"\n📁 Checking for debug files...")
    try:
        response = requests.get("http://localhost:8000/debug/json-files")
        if response.status_code == 200:
            data = response.json()
            print(f"Found {data['total_files']} debug files")
            
            if data['debug_files']:
                for file_info in data['debug_files']:
                    print(f"  📄 {file_info['filename']} ({file_info['size']} bytes)")
            else:
                print("  📭 No debug files (JSON repair was successful)")
        else:
            print(f"❌ Could not check debug files: {response.status_code}")
    except Exception as e:
        print(f"❌ Error checking debug files: {e}")

def test_debug_folder_structure():
    """Test the debug folder structure and organization"""
    
    print(f"\n📁 Testing Debug Folder Structure")
    print("=" * 60)
    
    # Check if debug folder exists
    debug_folder = "debug_json_files"
    
    print(f"🔍 Checking for debug folder: {debug_folder}")
    
    if os.path.exists(debug_folder):
        print(f"✅ Debug folder exists")
        
        # List contents
        files = os.listdir(debug_folder)
        print(f"📄 Files in debug folder: {len(files)}")
        
        for file in files[:10]:  # Show first 10 files
            filepath = os.path.join(debug_folder, file)
            size = os.path.getsize(filepath)
            print(f"  - {file} ({size} bytes)")
            
        if len(files) > 10:
            print(f"  ... and {len(files) - 10} more files")
    else:
        print(f"📭 Debug folder does not exist yet")
        print(f"   (Will be created when first debug file is saved)")

def test_filename_patterns():
    """Test different filename patterns and expected transformations"""
    
    print(f"\n🏷️  Testing Filename Pattern Recognition")
    print("=" * 60)
    
    # Test cases showing expected transformations
    test_cases = [
        ("mehak_jain_resume.pdf", "mehakjainresume(pdf)"),
        ("John Doe CV.docx", "johndoecv(docx)"),
        ("resume-2024-final.pdf", "resume2024final(pdf)"),
        ("<EMAIL>", "candidatecompanycom(pdf)"),
        ("My Resume (Updated).docx", "myresumeupdated(docx)"),
        ("software_engineer_resume.doc", "softwareengineerresume(doc)"),
    ]
    
    print("Expected filename transformations:")
    print("(Original filename → Expected debug filename)")
    print()
    
    for original, expected in test_cases:
        print(f"  {original}")
        print(f"  → {expected}.json")
        print(f"  → {expected}1.json (if collision)")
        print(f"  → {expected}2.json (if collision)")
        print()

def test_api_endpoints():
    """Test all debug-related API endpoints"""
    
    print(f"\n🔗 Testing Debug API Endpoints")
    print("=" * 60)
    
    endpoints = [
        ("GET", "/debug/json-files", "List debug files"),
        ("DELETE", "/debug/json-files", "Cleanup debug files"),
        ("POST", "/debug/repair-json", "Test JSON repair"),
    ]
    
    base_url = "http://localhost:8000"
    
    for method, endpoint, description in endpoints:
        print(f"\n🔗 {method} {endpoint}")
        print(f"   {description}")
        
        try:
            if method == "GET":
                response = requests.get(f"{base_url}{endpoint}")
            elif method == "DELETE":
                response = requests.delete(f"{base_url}{endpoint}")
            elif method == "POST" and "repair-json" in endpoint:
                # Test with simple malformed JSON
                response = requests.post(
                    f"{base_url}{endpoint}",
                    json={
                        "malformed_json": '{"name": "Test",}',  # Trailing comma
                        "context": "resume"
                    }
                )
            
            print(f"   📡 Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if method == "GET" and "json-files" in endpoint:
                    print(f"   📄 Files found: {result.get('total_files', 0)}")
                elif method == "DELETE":
                    print(f"   🧹 Files deleted: {result.get('total_deleted', 0)}")
                elif method == "POST":
                    print(f"   🔧 Repair status: {result.get('status', 'unknown')}")
            else:
                print(f"   ❌ Error: {response.text[:100]}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")

if __name__ == "__main__":
    print("🚀 Comprehensive Debug System Test")
    print("Testing the enhanced debug filename system with meaningful names...")
    
    # Test 1: JSON repair and debug file creation
    test_json_repair_with_debug_files()
    
    # Test 2: Debug folder structure
    test_debug_folder_structure()
    
    # Test 3: Filename patterns
    test_filename_patterns()
    
    # Test 4: API endpoints
    test_api_endpoints()
    
    print("\n" + "=" * 60)
    print("🎉 Comprehensive testing completed!")
    print("\n📋 Summary of Enhanced Debug System:")
    print("✅ Meaningful filenames based on source document")
    print("✅ Organized in dedicated debug_json_files folder")
    print("✅ Automatic collision handling with incremental numbers")
    print("✅ Enhanced API endpoints for debugging")
    print("✅ LLM self-healing integration")
    print("✅ Comprehensive error analysis and suggestions")
    
    print("\n🎯 Next Steps:")
    print("1. Process actual PDF/DOCX resumes to test real scenarios")
    print("2. Monitor debug_json_files folder for meaningful names")
    print("3. Use debug endpoints to analyze and resolve JSON issues")
    print("4. Leverage LLM self-healing for automatic problem resolution")
