{"total_score": 4.63, "fit_category": "Moderate Match", "summary": "The candidate is a moderate match for this position with a CGPA-style score of 4.6/10. Key strengths: Education. Areas for improvement: Skills, Certifications.", "skills_score": {"raw_score": 3.2, "normalized_score": 3.2, "weight": 4.0, "weighted_score": 12.8, "rationale": "Matched 2/5 required skills and 0/1 preferred skills. Matched: HTML, CSS. Missing: Javascript, Media queries, SDLC", "details": {"resume_skills_count": 26, "required_skills_count": 5, "preferred_skills_count": 1, "matched_required": ["HTML", "CSS"], "missing_required": ["Javascript", "Media queries", "SDLC"], "matched_preferred": [], "required_match_ratio": 0.4, "preferred_match_ratio": 0.0}}, "experience_score": {"raw_score": 5.0, "normalized_score": 5.0, "weight": 3.0, "weighted_score": 15.0, "rationale": "Could not determine candidate's experience to compare with required 1 years", "details": {"candidate_yoe": 0, "required_yoe": 1, "experience_ratio": 0.0, "experience_entries_count": 2}}, "education_score": {"raw_score": 8.0, "normalized_score": 8.0, "weight": 2.0, "weighted_score": 16.0, "rationale": "Education requirements met: 'B.Tech Computer Science Engineering (Artificial Intelligence & Machine Learning)' matches requirement 'Bachelor’s degree in IT, Computer Science, Software Engineering, or a related field'", "details": {"education_entries_count": 1, "education_requirements_count": 1, "education_match": true, "matched_degree": "B.Tech Computer Science Engineering (Artificial Intelligence & Machine Learning)", "matched_requirement": "Bachelor’s degree in IT, Computer Science, Software Engineering, or a related field", "candidate_degrees": ["B.Tech Computer Science Engineering (Artificial Intelligence & Machine Learning)"], "max_gpa_percentage": 0}}, "certifications_score": {"raw_score": 0.0, "normalized_score": 0.0, "weight": 0.5, "weighted_score": 0.0, "rationale": "No certifications found in resume", "details": {"total_certifications": 0, "relevant_certifications": [], "relevant_count": 0, "all_certifications": []}}, "location_score": {"raw_score": 5.0, "normalized_score": 5.0, "weight": 0.5, "weighted_score": 2.5, "rationale": "Location information not available for comparison", "details": {"jd_location": "", "resume_location": "", "experience_locations": [], "has_location_info": false}}, "reliability_score": {"raw_score": 5.0, "normalized_score": 5.0, "weight": 0.0, "weighted_score": 0.0, "rationale": "Could not calculate job stability due to missing experience data", "details": {"candidate_yoe": 0, "num_companies": 2, "avg_tenure": 0.0, "has_experience_data": false}}, "total_credits_used": 10.0, "calculation_method": "CGPA", "processing_time": 0.007028818130493164}